# Force UTF-8 encoding for Gradle properties
org.gradle.encoding=UTF-8

# =============================================
# Kotlin Code Style Configuration
# =============================================

# Enforce the official Kotlin code style for consistency across the project.
# Options:
# - 'official': Follows the official Kotlin coding conventions.
# - 'intellij': Uses IntelliJ IDEA's default Kotlin style.
kotlin.code.style=official

# ===========================================================
# JVM Memory and Performance Settings for Gradle
# ===========================================================

# Configure JVM arguments for the Gradle Daemon.
# These settings control memory usage, garbage collection, and character encoding.
#
# Arguments explained:
# -Xmx2G                        // Maximum Heap Size: Allocates up to 2 GB of heap memory for Gradle's JVM process.
#                                This prevents OutOfMemoryError during large builds.
#
# -Xms512M                      // Initial Heap Size: Allocates 512 MB of memory to the JVM at startup.
#                                Reduces the need for dynamic memory allocation during build execution.
#
# -XX:MaxMetaspaceSize=512M     // Maximum Metaspace Size: Limits the memory allocated for class metadata to 512 MB.
#                                Increases stability when many classes and plugins are loaded.
#
# -XX:+HeapDumpOnOutOfMemoryError // Enables automatic heap dumps if an OutOfMemoryError occurs.
#                                   Useful for debugging memory leaks (heap dump file will be created for analysis).
#
# -Dfile.encoding=UTF-8         // Forces UTF-8 character encoding across all systems.
#                                Ensures consistent behavior across different platforms.
#
# Best Practice:
# Ensure that total memory usage (considering worker threads) does not exceed available physical RAM.
# Example: If you have 16 GB RAM, keep total JVM memory for Gradle under 12 GB for optimal performance.
org.gradle.jvmargs=-Xmx2G -Xms512M -XX:MaxMetaspaceSize=512M -Dfile.encoding=UTF-8

# =============================================
# Parallel Build Execution
# =============================================

# Enables parallel execution of independent tasks.
# This can significantly speed up the build on multi-core systems.
# Enabled by default in Gradle 6.0 and above, but this ensures it's explicitly set.
org.gradle.parallel=true

# =============================================
# Gradle Daemon Configuration
# =============================================

# Enables the Gradle daemon to improve performance by reusing JVM processes
# across builds. This reduces startup time for subsequent builds.
# Recommended for most development environments.
org.gradle.daemon=true

# =============================================
# Worker Configuration (Concurrency Control)
# =============================================

# Specifies the maximum number of concurrent worker threads.
# Each worker uses a separate JVM process with its own memory allocation
# as defined in org.gradle.jvmargs.
#
# Recommendation:
# - Set this to (number of CPU cores - 1) for optimal usage.
# - Adjust depending on available memory.
#
# Example:
# - 4 workers: Up to (4 x 2 GB) heap + additional memory overhead.
org.gradle.workers.max=8

# =============================================
# Daemon Timeout (Optional)
# =============================================

# Controls how long an idle daemon will remain alive before stopping (in milliseconds).
# This reduces memory usage by automatically shutting down inactive daemons.
# Example: 60 minutes (6,000,000 milliseconds)
org.gradle.daemon.idletimeout=6000000

# =============================================
# Caching (Optional, for faster builds)
# =============================================

# Enables Gradle's build cache, which stores task outputs for reuse
# in future builds to speed up execution time.
org.gradle.caching=true

#Kotlin
# Set the maximum heap size for the Kotlin daemon
kotlin.daemon.jvmargs=-Xmx3072M

#Gradle
# Enable the configuration cache for faster builds. This caches the build configuration
# and reuses it for subsequent builds.
org.gradle.configuration-cache=true
# Enable VFS watching for faster builds.
# This watches the file system for changes and invalidates the cache accordingly.
org.gradle.vfs.watch=true

#Android
android.nonTransitiveRClass=true
android.useAndroidX=true