package eu.torvian.chatbot.app.koin

import arrow.core.getOrElse
import arrow.core.raise.either
import eu.torvian.chatbot.app.service.api.ktor.createHttpClient
import eu.torvian.chatbot.app.service.auth.TokenStorage
import eu.torvian.chatbot.app.utils.misc.createKmpLogger
import eu.torvian.chatbot.common.api.resources.AuthResource
import eu.torvian.chatbot.common.models.auth.LoginResponse
import eu.torvian.chatbot.common.models.auth.RefreshTokenRequest
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.serialization.json.Json

/**
 * Creates an authenticated HTTP client with token refresh capabilities.
 *
 * This function removes circular dependencies by manually handling refresh token calls
 * instead of using AuthApi and AuthRepository components.
 *
 * @param baseUri Base URI for the API
 * @param json JSON serializer configuration
 * @param tokenStorage Token storage implementation
 * @param unauthenticatedHttpClient HTTP client for refresh token calls
 * @return A configured HttpClient with authentication support
 *
 * TODO: Notify AuthRepository on failures (with EventBus?)
 */
fun createAuthenticatedHttpClient(
    baseUri: String,
    json: Json,
    tokenStorage: TokenStorage,
    unauthenticatedHttpClient: HttpClient
): HttpClient {
    return createHttpClient(baseUri, json).config {
        install(Auth) {
            bearer {
                loadTokens {
                    // Bridge our Either-based storage to the plugin's nullable BearerTokens
                    either {
                        val accessToken = tokenStorage.getAccessToken().bind()
                        val refreshToken = tokenStorage.getRefreshToken().bind()
                        BearerTokens(accessToken, refreshToken)
                    }.getOrElse {
                        logger.warn("Failed to load tokens: ${it.message}")
                        null
                    }
                }

                refreshTokens {
                    val oldRefreshToken = oldTokens?.refreshToken ?: run {
                        logger.warn("No refresh token available, clearing tokens")
                        // No refresh token available, clear tokens
                        tokenStorage.clearTokens()
                        return@refreshTokens null
                    }

                    try {
                        // Manual refresh token call using the provided unauthenticated client
                        val refreshResponse: LoginResponse = unauthenticatedHttpClient.post(AuthResource.Refresh()) {
                            setBody(RefreshTokenRequest(oldRefreshToken))
                        }.body()

                        // Save the new tokens
                        tokenStorage.saveTokens(
                            refreshResponse.accessToken,
                            refreshResponse.refreshToken,
                            refreshResponse.expiresAt
                        ).fold(
                            ifLeft = {
                                logger.warn("Failed to save tokens after refresh: ${it.message}")
                                // Failed to save tokens locally, clear them
                                tokenStorage.clearTokens()
                                null
                            },
                            ifRight = {
                                // Return new tokens for the auth plugin
                                BearerTokens(refreshResponse.accessToken, refreshResponse.refreshToken)
                            }
                        )
                    } catch (_: Exception) {
                        logger.warn("Refresh token request failed, clearing tokens")
                        // Refresh was rejected by the server or network error, clear tokens
                        tokenStorage.clearTokens()
                        null // Return null to signify refresh failure
                    }
                }

                sendWithoutRequest { request ->
                    // Don't send auth tokens to auth endpoints to prevent infinite loops
                    !request.url.encodedPath.contains("/api/v1/auth/")
                }
            }
        }
    }
}

private val logger = createKmpLogger("createAuthenticatedHttpClient")