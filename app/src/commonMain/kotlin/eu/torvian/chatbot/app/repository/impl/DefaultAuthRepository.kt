package eu.torvian.chatbot.app.repository.impl

import arrow.core.Either
import eu.torvian.chatbot.app.repository.AuthRepository
import eu.torvian.chatbot.app.repository.AuthState
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.app.repository.toRepositoryError
import eu.torvian.chatbot.app.service.api.AuthApi
import eu.torvian.chatbot.app.service.auth.TokenStorage
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.models.User
import eu.torvian.chatbot.common.models.auth.LoginRequest
import eu.torvian.chatbot.common.models.auth.RegisterRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Default implementation of the AuthRepository interface.
 *
 * This repository manages authentication state using StateFlow and coordinates
 * between the AuthApi for server operations and TokenStorage for local token management.
 *
 * @property authApi The API client for authentication operations
 * @property tokenStorage The storage for managing authentication tokens
 */
class DefaultAuthRepository(
    private val authApi: AuthApi,
    private val tokenStorage: TokenStorage
) : AuthRepository {

    companion object {
        private val logger = kmpLogger<DefaultAuthRepository>()
    }

    private val _authState = MutableStateFlow<AuthState>(AuthState.Unauthenticated)
    override val authState: StateFlow<AuthState> = _authState.asStateFlow()

    override suspend fun login(request: LoginRequest): Either<RepositoryError, Unit> {
        _authState.value = AuthState.Loading

        return authApi.login(request)
            .mapLeft { apiError ->
                _authState.value = AuthState.Unauthenticated
                apiError.toRepositoryError("Login failed")
            }
            .map { loginResponse ->
                // Save tokens to storage
                tokenStorage.saveTokens(
                    accessToken = loginResponse.accessToken,
                    refreshToken = loginResponse.refreshToken,
                    expiresAt = loginResponse.expiresAt
                ).fold(
                    ifLeft = { tokenError ->
                        logger.warn("Failed to save tokens after successful login: ${tokenError.message}")
                        _authState.value = AuthState.Unauthenticated
                        return Either.Left(RepositoryError.OtherError("Failed to save authentication tokens"))
                    },
                    ifRight = {
                        _authState.value = AuthState.Authenticated(
                            userId = loginResponse.user.id,
                            username = loginResponse.user.username
                        )
                    }
                )
            }
    }

    override suspend fun register(request: RegisterRequest): Either<RepositoryError, User> {
        return authApi.register(request)
            .mapLeft { apiError ->
                apiError.toRepositoryError("Registration failed")
            }
    }

    override suspend fun logout(): Either<RepositoryError, Unit> {
        return authApi.logout()
            .map {
                // Only clear tokens on successful server logout
                tokenStorage.clearTokens()
                _authState.value = AuthState.Unauthenticated
            }
            .mapLeft { apiError ->
                apiError.toRepositoryError("Logout failed")
            }
    }

    override suspend fun cleanupLogout() {
        // Always clear local tokens and update state without contacting server
        tokenStorage.clearTokens()
        _authState.value = AuthState.Unauthenticated
    }

    override suspend fun isAuthenticated(): Boolean {
        return _authState.value is AuthState.Authenticated
    }
}
