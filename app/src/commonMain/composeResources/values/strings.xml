<resources>
    <!-- App General -->
    <string name="app_name">Chatbot app</string>

    <!-- Action Labels -->
    <string name="action_retry">Retry</string>
    <string name="action_settings">Settings</string>
    <string name="action_chat">Chat</string>

    <!-- Button Labels -->
    <string name="label_settings_button">Settings</string>
    <string name="label_chat_button">Chat</string>
    <string name="label_send_button">Send</string>
    <string name="label_cancel_button">Cancel</string>

    <!-- Error Messages -->
    <string name="error_api_request">An API error occurred: %1$s\nCause: %2$s</string>
    <string name="error_repository">A data access error occurred: %1$s\nCause: %2$s</string>
    <string name="error_unknown">An unexpected error occurred.</string>
    <string name="error_loading_sessions_groups">Failed to load sessions or groups.</string>
    <string name="error_loading_session">Failed to load session.</string>
    <string name="error_sending_message_short">Failed to send message</string>
    <string name="error_updating_session_model">Failed to update session model</string>
    <string name="error_updating_session_settings">Failed to update session settings</string>
    <string name="error_switching_branch">Failed to switch conversation branch</string>
    <string name="error_unsupported_model_type">Unsupported model type</string>

    <!-- Warning Messages -->
    <string name="warning_model_or_settings_unavailable">Cannot send message: model or settings are not available</string>

    <!-- Input Area -->
    <string name="replying_to_prefix">Replying to:</string>
    <string name="send_message_button_description">Send message</string>
    <string name="cancel_reply_button_description">Cancel reply</string>

    <!-- Success Messages -->
    <string name="operation_success">Operation completed successfully.</string>

</resources>