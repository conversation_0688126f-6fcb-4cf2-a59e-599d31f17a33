package eu.torvian.chatbot.app.service.auth
//
//import arrow.core.Either
//import arrow.core.left
//import arrow.core.right
//import kotlinx.datetime.Clock
//import kotlinx.datetime.Instant
//import kotlinx.serialization.Serializable
//import kotlinx.serialization.json.Json
//import kotlinx.serialization.json.jsonObject
//import kotlinx.serialization.json.jsonPrimitive
//import kotlinx.serialization.json.long
//import kotlin.random.Random
//
///**
// * Web implementation of [TokenStorage] using encrypted localStorage.
// *
// * Tokens are stored in the browser's localStorage with encryption.
// * Falls back to sessionStorage if localStorage is unavailable.
// */
//class WebTokenStorage(
//    private val json: Json = Json { ignoreUnknownKeys = true }
//) : TokenStorage {
//
//    companion object {
//        private const val TOKEN_STORAGE_KEY = "chatbot_tokens"
//        private const val ENCRYPTION_KEY_KEY = "chatbot_key"
//    }
//
//    @Serializable
//    private data class TokenData(
//        val accessToken: String,
//        val refreshToken: String,
//        val expiresAt: Long, // Instant as epoch seconds
//        val iv: String // Base64 encoded IV for decryption
//    )
//
//    private val storage = getAvailableStorage()
//    private val encryptionKey = getOrCreateEncryptionKey()
//
//    override suspend fun saveTokens(
//        accessToken: String,
//        refreshToken: String,
//        expiresAt: Instant
//    ): Either<TokenStorageError, Unit> {
//        return try {
//            val iv = generateRandomBytes(16)
//            val encryptedAccess = encrypt(accessToken, encryptionKey, iv)
//            val encryptedRefresh = encrypt(refreshToken, encryptionKey, iv)
//
//            val tokenData = TokenData(
//                accessToken = encryptedAccess,
//                refreshToken = encryptedRefresh,
//                expiresAt = expiresAt.epochSeconds,
//                iv = bytesToBase64(iv)
//            )
//
//            val jsonString = json.encodeToString(tokenData)
//            storage.setItem(TOKEN_STORAGE_KEY, jsonString)
//
//            Unit.right()
//        } catch (e: Exception) {
//            when {
//                e.message?.contains("quota", ignoreCase = true) == true ||
//                        e.message?.contains("storage", ignoreCase = true) == true ->
//                    TokenStorageError.IOError("Browser storage quota exceeded or unavailable", e).left()
//
//                e.message?.contains("encrypt", ignoreCase = true) == true ->
//                    TokenStorageError.EncryptionError("Failed to encrypt token data", e).left()
//
//                else ->
//                    TokenStorageError.Unknown("Failed to save tokens", e).left()
//            }
//        }
//    }
//
//    override suspend fun getAccessToken(): Either<TokenStorageError, String> {
//        return getTokenData().fold(
//            { error -> error.left() },
//            { tokenData ->
//                try {
//                    val iv = base64ToBytes(tokenData.iv)
//                    val decrypted = decrypt(tokenData.accessToken, encryptionKey, iv)
//                    decrypted.right()
//                } catch (e: Exception) {
//                    TokenStorageError.EncryptionError("Failed to decrypt access token", e).left()
//                }
//            }
//        )
//    }
//
//    override suspend fun getRefreshToken(): Either<TokenStorageError, String> {
//        return getTokenData().fold(
//            { error -> error.left() },
//            { tokenData ->
//                try {
//                    val iv = base64ToBytes(tokenData.iv)
//                    val decrypted = decrypt(tokenData.refreshToken, encryptionKey, iv)
//                    decrypted.right()
//                } catch (e: Exception) {
//                    TokenStorageError.EncryptionError("Failed to decrypt refresh token", e).left()
//                }
//            }
//        )
//    }
//
//    override suspend fun getExpiry(): Either<TokenStorageError, Instant> {
//        return getTokenData().map { Instant.fromEpochSeconds(it.expiresAt) }
//    }
//
//    override suspend fun clearTokens(): Either<TokenStorageError, Unit> {
//        return try {
//            storage.removeItem(TOKEN_STORAGE_KEY)
//            Unit.right()
//        } catch (e: Exception) {
//            TokenStorageError.IOError("Failed to clear tokens from storage", e).left()
//        }
//    }
//
//    override suspend fun isTokenExpired(token: String): Either<TokenStorageError, Boolean> {
//        return try {
//            // Try to parse JWT token to get expiry
//            val parts = token.split(".")
//            if (parts.size != 3) {
//                return TokenStorageError.InvalidTokenFormat("Invalid JWT format").left()
//            }
//
//            // Decode payload (second part) - handle URL-safe base64
//            val paddedPayload = parts[1].let { payload ->
//                when (payload.length % 4) {
//                    2 -> payload + "=="
//                    3 -> payload + "="
//                    else -> payload
//                }
//            }
//
//            // Use browser's atob function for base64 decoding
//            val payloadBytes = js("window.atob(arguments[0])")
//                .unsafeCast<(String) -> String>()
//                .invoke(paddedPayload.replace('-', '+').replace('_', '/'))
//
//            val payloadJson = json.parseToJsonElement(payloadBytes).jsonObject
//
//            val exp = payloadJson["exp"]?.jsonPrimitive?.long
//                ?: return TokenStorageError.InvalidTokenFormat("Missing 'exp' claim in JWT").left()
//
//            val expiryTime = Instant.fromEpochSeconds(exp)
//            val now = Clock.System.now()
//
//            (now > expiryTime).right()
//        } catch (e: Exception) {
//            TokenStorageError.InvalidTokenFormat("Failed to parse JWT token", e).left()
//        }
//    }
//
//    private fun getTokenData(): Either<TokenStorageError, TokenData> {
//        return try {
//            val jsonString = storage.getItem(TOKEN_STORAGE_KEY)
//                ?: return TokenStorageError.NotFound("No tokens stored").left()
//
//            val tokenData = json.decodeFromString<TokenData>(jsonString)
//            tokenData.right()
//        } catch (e: Exception) {
//            when {
//                e.message?.contains("serialization", ignoreCase = true) == true ->
//                    TokenStorageError.InvalidTokenFormat("Failed to parse stored token data", e).left()
//
//                else ->
//                    TokenStorageError.IOError("Failed to read tokens from storage", e).left()
//            }
//        }
//    }
//
//    private fun getAvailableStorage(): Storage {
//        return try {
//            // Test localStorage availability
//            val localStorage = js("window.localStorage").unsafeCast<Storage?>()
//            if (localStorage != null) {
//                localStorage.setItem("test", "test")
//                localStorage.removeItem("test")
//                localStorage
//            } else {
//                js("window.sessionStorage").unsafeCast<Storage>()
//            }
//        } catch (_: Exception) {
//            // Fallback to sessionStorage
//            try {
//                js("window.sessionStorage").unsafeCast<Storage>()
//            } catch (e2: Exception) {
//                throw RuntimeException("Neither localStorage nor sessionStorage is available", e2)
//            }
//        }
//    }
//
//    private fun getOrCreateEncryptionKey(): ByteArray {
//        return try {
//            val stored = storage.getItem(ENCRYPTION_KEY_KEY)
//            if (stored != null) {
//                base64ToBytes(stored)
//            } else {
//                val key = generateRandomBytes(32) // 256-bit key
//                storage.setItem(ENCRYPTION_KEY_KEY, bytesToBase64(key))
//                key
//            }
//        } catch (_: Exception) {
//            // Generate a session-only key if storage fails
//            generateRandomBytes(32)
//        }
//    }
//
//    private fun generateRandomBytes(size: Int): ByteArray {
//        return Random.nextBytes(size)
//    }
//
//    private fun bytesToBase64(bytes: ByteArray): String {
//        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
//        var result = ""
//        var i = 0
//        while (i < bytes.size) {
//            val b1 = bytes[i].toInt() and 0xFF
//            val b2 = if (i + 1 < bytes.size) bytes[i + 1].toInt() and 0xFF else 0
//            val b3 = if (i + 2 < bytes.size) bytes[i + 2].toInt() and 0xFF else 0
//
//            val bitmap = (b1 shl 16) or (b2 shl 8) or b3
//
//            result += chars[(bitmap shr 18) and 63]
//            result += chars[(bitmap shr 12) and 63]
//            result += if (i + 1 < bytes.size) chars[(bitmap shr 6) and 63] else '='
//            result += if (i + 2 < bytes.size) chars[bitmap and 63] else '='
//
//            i += 3
//        }
//        return result
//    }
//
//    private fun base64ToBytes(base64: String): ByteArray {
//        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
//        val charMap = chars.mapIndexed { index, char -> char to index }.toMap()
//
//        val cleanBase64 = base64.replace("=", "")
//        val result = mutableListOf<Byte>()
//
//        var i = 0
//        while (i < cleanBase64.length) {
//            val b1 = charMap[cleanBase64[i]] ?: 0
//            val b2 = if (i + 1 < cleanBase64.length) charMap[cleanBase64[i + 1]] ?: 0 else 0
//            val b3 = if (i + 2 < cleanBase64.length) charMap[cleanBase64[i + 2]] ?: 0 else 0
//            val b4 = if (i + 3 < cleanBase64.length) charMap[cleanBase64[i + 3]] ?: 0 else 0
//
//            val bitmap = (b1 shl 18) or (b2 shl 12) or (b3 shl 6) or b4
//
//            result.add(((bitmap shr 16) and 0xFF).toByte())
//            if (i + 2 < cleanBase64.length) result.add(((bitmap shr 8) and 0xFF).toByte())
//            if (i + 3 < cleanBase64.length) result.add((bitmap and 0xFF).toByte())
//
//            i += 4
//        }
//        return result.toByteArray()
//    }
//
//    // Simple XOR encryption for web platform (sufficient for client-side token protection)
//    private fun encrypt(data: String, key: ByteArray, iv: ByteArray): String {
//        val dataBytes = data.encodeToByteArray()
//        val encrypted = ByteArray(dataBytes.size)
//
//        for (i in dataBytes.indices) {
//            encrypted[i] = (dataBytes[i].toInt() xor key[i % key.size].toInt() xor iv[i % iv.size].toInt()).toByte()
//        }
//
//        return bytesToBase64(encrypted)
//    }
//
//    private fun decrypt(encryptedData: String, key: ByteArray, iv: ByteArray): String {
//        val encrypted = base64ToBytes(encryptedData)
//        val decrypted = ByteArray(encrypted.size)
//
//        for (i in encrypted.indices) {
//            decrypted[i] = (encrypted[i].toInt() xor key[i % key.size].toInt() xor iv[i % iv.size].toInt()).toByte()
//        }
//
//        return decrypted.decodeToString()
//    }
//}
//
//// External interface for browser Storage API
//external interface Storage {
//    fun getItem(key: String): String?
//    fun setItem(key: String, value: String)
//    fun removeItem(key: String)
//}
