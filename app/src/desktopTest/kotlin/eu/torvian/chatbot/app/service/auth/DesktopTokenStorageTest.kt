package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.security.CryptoError
import eu.torvian.chatbot.common.security.CryptoProvider
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import kotlinx.serialization.json.Json
import java.io.File
import java.util.*
import kotlin.test.*
import kotlin.time.Duration.Companion.hours

/**
 * Unit tests for [DesktopTokenStorage] implementation.
 */
class DesktopTokenStorageTest {

    private fun createTestTokenStorage(): DesktopTokenStorage {
        // Create a unique test storage that doesn't interfere with other tests
        return TestDesktopTokenStorage()
    }

    @Test
    fun `saveTokens and getTokens should work correctly`() = runTest {
        val tokenStorage = createTestTokenStorage()

        // Arrange
        val accessToken = "test.access.token"
        val refreshToken = "test.refresh.token"
        val expiresAt = Clock.System.now() + 1.hours

        // Act
        val saveResult = tokenStorage.saveTokens(accessToken, refreshToken, expiresAt)

        // Assert save succeeded - debug if it fails
        if (saveResult.isLeft()) {
            println("Save failed with error: ${saveResult.leftOrNull()?.message}")
        }
        assertTrue(saveResult.isRight())

        // Act - retrieve tokens
        val accessResult = tokenStorage.getAccessToken()
        val refreshResult = tokenStorage.getRefreshToken()
        val expiryResult = tokenStorage.getExpiry()

        // Assert retrieval succeeded - debug if they fail
        if (accessResult.isLeft()) {
            println("Access token retrieval failed: ${accessResult.leftOrNull()?.message}")
        }
        if (refreshResult.isLeft()) {
            println("Refresh token retrieval failed: ${refreshResult.leftOrNull()?.message}")
        }
        if (expiryResult.isLeft()) {
            println("Expiry retrieval failed: ${expiryResult.leftOrNull()?.message}")
        }

        assertTrue(accessResult.isRight())
        assertTrue(refreshResult.isRight())
        assertTrue(expiryResult.isRight())

        assertEquals(accessToken, accessResult.getOrNull())
        assertEquals(refreshToken, refreshResult.getOrNull())
        assertEquals(expiresAt.epochSeconds, expiryResult.getOrNull()?.epochSeconds)
    }

    @Test
    fun `getAccessToken should return NotFound when no tokens stored`() = runTest {
        val tokenStorage = createTestTokenStorage()

        // Act - no need to clear tokens since this is a fresh instance
        val result = tokenStorage.getAccessToken()

        // Assert
        assertTrue(result.isLeft())
        assertTrue(result.leftOrNull() is TokenStorageError.NotFound)
    }

    @Test
    fun `clearTokens should remove all stored tokens`() = runTest {
        val tokenStorage = createTestTokenStorage()

        // Arrange - save some tokens first
        val accessToken = "test.access.token"
        val refreshToken = "test.refresh.token"
        val expiresAt = Clock.System.now() + 1.hours

        tokenStorage.saveTokens(accessToken, refreshToken, expiresAt)

        // Act
        val clearResult = tokenStorage.clearTokens()

        // Assert clear succeeded
        assertTrue(clearResult.isRight())

        // Assert tokens are gone
        val accessResult = tokenStorage.getAccessToken()
        assertTrue(accessResult.isLeft())
        assertTrue(accessResult.leftOrNull() is TokenStorageError.NotFound)
    }

    @Test
    fun `active re-encryption should work when KEK version changes`() = runTest {
        val tokenStorage = createTestTokenStorage() as TestDesktopTokenStorage

        // Arrange - save tokens with KEK version 1
        val accessToken = "test.access.token"
        val refreshToken = "test.refresh.token"
        val expiresAt = Clock.System.now() + 1.hours

        tokenStorage.saveTokens(accessToken, refreshToken, expiresAt)

        // Simulate KEK version change
        tokenStorage.mockCryptoProvider.currentKekVersion = 2

        // Act - retrieve tokens, which should trigger active re-encryption
        val accessResult = tokenStorage.getAccessToken()

        // Assert - tokens should still be retrievable
        assertTrue(accessResult.isRight())
        assertEquals(accessToken, accessResult.getOrNull())

        // Verify that the re-encryption was attempted (the mock tracks this)
        assertTrue(tokenStorage.mockCryptoProvider.reEncryptionAttempted)
    }

    @Test
    fun `should handle missing key file gracefully`() = runTest {
        val tokenStorage = createTestTokenStorage() as TestDesktopTokenStorage

        // Create only the data file without the key file
        tokenStorage.testDataFile.parentFile?.mkdirs()
        tokenStorage.testDataFile.writeText("encrypted-data")

        // Act
        val result = tokenStorage.getAccessToken()

        // Assert
        assertTrue(result.isLeft())
        assertTrue(result.leftOrNull() is TokenStorageError.NotFound)
    }

    @Test
    fun `should handle missing data file gracefully`() = runTest {
        val tokenStorage = createTestTokenStorage() as TestDesktopTokenStorage

        // Create only the key file without the data file
        tokenStorage.testKeyFile.parentFile?.mkdirs()
        tokenStorage.testKeyFile.writeText("""{"wrappedDek":"test","kekVersion":1}""")

        // Act
        val result = tokenStorage.getAccessToken()

        // Assert
        assertTrue(result.isLeft())
        assertTrue(result.leftOrNull() is TokenStorageError.NotFound)
    }

    @Test
    fun `should handle corrupted metadata file gracefully`() = runTest {
        val tokenStorage = createTestTokenStorage() as TestDesktopTokenStorage

        // Create corrupted metadata file
        tokenStorage.testKeyFile.parentFile?.mkdirs()
        tokenStorage.testKeyFile.writeText("invalid-json")
        tokenStorage.testDataFile.writeText("encrypted-data")

        // Act
        val result = tokenStorage.getAccessToken()

        // Assert
        assertTrue(result.isLeft())
        assertTrue(result.leftOrNull() is TokenStorageError.InvalidTokenFormat)
    }
}

/**
 * Mock implementation of CryptoProvider for testing purposes.
 */
private class MockCryptoProvider : CryptoProvider {
    var currentKekVersion = 1
    var reEncryptionAttempted = false

    override fun generateDEK(): Either<CryptoError, String> {
        return "mock-dek-${UUID.randomUUID()}".right()
    }

    override fun encryptData(plainText: String, dek: String): Either<CryptoError, String> {
        // Simple mock encryption: just base64 encode the plaintext with a prefix
        return "encrypted:${Base64.getEncoder().encodeToString(plainText.toByteArray())}".right()
    }

    override fun decryptData(cipherText: String, dek: String): Either<CryptoError, String> {
        // Simple mock decryption: remove prefix and base64 decode
        if (!cipherText.startsWith("encrypted:")) {
            return CryptoError.InvalidCiphertext("Invalid cipher text format").left()
        }
        val base64Part = cipherText.removePrefix("encrypted:")
        return try {
            String(Base64.getDecoder().decode(base64Part)).right()
        } catch (e: Exception) {
            CryptoError.DecryptionError("Failed to decode: ${e.message}").left()
        }
    }

    override fun wrapDEK(dek: String): Either<CryptoError, String> {
        // Track when re-encryption is attempted (when called for an existing DEK)
        if (dek.startsWith("mock-dek-")) {
            reEncryptionAttempted = true
        }
        return "wrapped:$dek:version:$currentKekVersion".right()
    }

    override fun unwrapDEK(wrappedDek: String, kekVersion: Int): Either<CryptoError, String> {
        if (!wrappedDek.startsWith("wrapped:") || !wrappedDek.contains(":version:$kekVersion")) {
            return CryptoError.KeyVersionNotFound(kekVersion).left()
        }
        // Extract the DEK from the wrapped format
        val parts = wrappedDek.split(":")
        if (parts.size != 4) {
            return CryptoError.InvalidCiphertext("Invalid wrapped DEK format").left()
        }
        return parts[1].right() // Return the DEK part
    }

    override fun getKeyVersion(): Int = currentKekVersion
}

/**
 * Test implementation of DesktopTokenStorage that uses unique file paths per test instance
 */
private class TestDesktopTokenStorage(
    val mockCryptoProvider: MockCryptoProvider = MockCryptoProvider()
) : DesktopTokenStorage(
    mockCryptoProvider,
    System.getProperty("java.io.tmpdir") + "/chatbot-test-${UUID.randomUUID()}",
    Json { ignoreUnknownKeys = true }
) {
    private val testId = UUID.randomUUID().toString()
    val testKeyFile = File(System.getProperty("java.io.tmpdir"), "chatbot-test-$testId-dek.json")
    val testDataFile = File(System.getProperty("java.io.tmpdir"), "chatbot-test-$testId-tokens.enc")

    override val keyFilePath: File get() = testKeyFile
    override val dataFilePath: File get() = testDataFile
}
