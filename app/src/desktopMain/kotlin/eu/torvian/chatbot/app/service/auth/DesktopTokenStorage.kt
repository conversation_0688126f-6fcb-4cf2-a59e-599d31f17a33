package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import arrow.core.left
import arrow.core.raise.either
import arrow.core.right
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.security.CryptoProvider
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermissions


/**
 * Desktop implementation of [TokenStorage] using envelope encryption with a two-file strategy.
 *
 * This implementation separates key metadata from encrypted data for more efficient
 * key rotation. It manages two files in the provided [storageDirectoryPath]:
 * - `dek.json`: Stores the wrapped Data Encryption Key (DEK) and the version of the
 *   Key Encryption Key (KEK) used to wrap it.
 * - `tokens.enc`: Stores the actual token data, encrypted with the DEK.
 *
 * It also implements **active re-encryption**: if data encrypted with an old KEK is
 * read, the DEK is automatically re-wrapped with the current KEK.
 *
 * @param cryptoProvider The provider for all cryptographic operations.
 * @param storageDirectoryPath The absolute path to the directory for storing the files.
 * @param json The JSON serializer instance.
 */
open class DesktopTokenStorage(
    private val cryptoProvider: CryptoProvider,
    storageDirectoryPath: String,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TokenStorage {

    private val logger = kmpLogger<DesktopTokenStorage>()

    @Serializable
    private data class TokenData(
        val accessToken: String,
        val refreshToken: String,
        val expiresAt: Long // Instant as epoch seconds
    )

    /**
     * A serializable container for the DEK metadata.
     * This is stored separately from the encrypted data.
     */
    @Serializable
    private data class DekMetadata(
        val wrappedDek: String,    // Base64 encoded wrapped Data Encryption Key
        val kekVersion: Int        // Version of the Key Encryption Key used
    )

    private val storageDir = File(storageDirectoryPath)
    protected open val keyFilePath: File = File(storageDir, "dek.json")
    protected open val dataFilePath: File = File(storageDir, "tokens.enc")

    override suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit> = either {
        try {
            // 1. Prepare the plaintext token data.
            val tokenData = TokenData(accessToken, refreshToken, expiresAt.epochSeconds)
            val plainTextJson = json.encodeToString(tokenData)

            // 2-4. Generate DEK, encrypt data, and wrap DEK using bind for sequential operations
            val dek = cryptoProvider.generateDEK()
                .mapLeft { TokenStorageError.EncryptionError("Failed to generate DEK: $it", null) }
                .bind()

            val encryptedData = cryptoProvider.encryptData(plainTextJson, dek)
                .mapLeft { TokenStorageError.EncryptionError("Failed to encrypt data: $it", null) }
                .bind()

            val wrappedDek = cryptoProvider.wrapDEK(dek)
                .mapLeft { TokenStorageError.EncryptionError("Failed to wrap DEK: $it", null) }
                .bind()

            val kekVersion = cryptoProvider.getKeyVersion()
            val dekMetadata = DekMetadata(wrappedDek, kekVersion)
            val metadataJson = json.encodeToString(dekMetadata)

            // 5. Write both files to disk with secure permissions.
            storageDir.mkdirs()
            dataFilePath.writeText(encryptedData)
            setFilePermissions(dataFilePath)
            keyFilePath.writeText(metadataJson)
            setFilePermissions(keyFilePath)
        } catch (e: Exception) {
            val error = when (e) {
                is java.io.IOException -> TokenStorageError.IOError("Failed to save tokens to file", e)
                else -> TokenStorageError.EncryptionError("Failed to encrypt token data for storage", e)
            }
            return error.left()
        }
    }

    override suspend fun getAccessToken(): Either<TokenStorageError, String> = getTokenData().map { it.accessToken }
    override suspend fun getRefreshToken(): Either<TokenStorageError, String> = getTokenData().map { it.refreshToken }
    override suspend fun getExpiry(): Either<TokenStorageError, Instant> =
        getTokenData().map { Instant.fromEpochSeconds(it.expiresAt) }

    override suspend fun clearTokens(): Either<TokenStorageError, Unit> {
        return try {
            if (keyFilePath.exists()) keyFilePath.delete()
            if (dataFilePath.exists()) dataFilePath.delete()
            Unit.right()
        } catch (e: Exception) {
            TokenStorageError.IOError("Failed to clear tokens", e).left()
        }
    }

    private fun getTokenData(): Either<TokenStorageError, TokenData> = either {
        try {
            if (!keyFilePath.exists() || !dataFilePath.exists()) {
                return TokenStorageError.NotFound("Token storage files not found").left()
            }

            // 1. Read metadata and encrypted data.
            val metadataJson = keyFilePath.readText()
            val dekMetadata = json.decodeFromString<DekMetadata>(metadataJson)
            val encryptedData = dataFilePath.readText()

            // 2-3. Unwrap DEK and decrypt data using bind for sequential operations
            val dek = cryptoProvider.unwrapDEK(dekMetadata.wrappedDek, dekMetadata.kekVersion)
                .mapLeft { TokenStorageError.EncryptionError("Failed to unwrap DEK: $it", null) }
                .bind()

            val decryptedJson = cryptoProvider.decryptData(encryptedData, dek)
                .mapLeft { TokenStorageError.EncryptionError("Failed to decrypt data: $it", null) }
                .bind()

            val tokenData = json.decodeFromString<TokenData>(decryptedJson)

            // 4. Active re-encryption if needed
            val currentKekVersion = cryptoProvider.getKeyVersion()
            if (dekMetadata.kekVersion != currentKekVersion) {
                performActiveDekReEncryption(dek, dekMetadata.kekVersion, currentKekVersion)
            }

            tokenData
        } catch (e: Exception) {
            val error = when (e) {
                is IOException, is SecurityException -> TokenStorageError.IOError("Failed to read tokens from file", e)
                is SerializationException -> TokenStorageError.InvalidTokenFormat(
                    "Failed to parse stored token data",
                    e
                )

                is IllegalArgumentException -> TokenStorageError.EncryptionError(
                    e.message ?: "Failed to find key for decryption", e
                )

                else -> TokenStorageError.EncryptionError("Failed to decrypt token data", e)
            }
            return error.left()
        }
    }

    /**
     * Re-wraps the provided plaintext DEK with the current KEK and overwrites the metadata file.
     * This is a "self-healing" mechanism to migrate keys over time.
     */
    private fun performActiveDekReEncryption(dek: String, oldKekVersion: Int, newKekVersion: Int) {
        try {
            logger.info("Performing active DEK re-encryption. Migrating from KEK version $oldKekVersion to $newKekVersion.")

            cryptoProvider.wrapDEK(dek).fold(
                { error -> logger.error("Failed to wrap DEK during re-encryption: $error") },
                { newWrappedDek ->
                    val newDekMetadata = DekMetadata(newWrappedDek, newKekVersion)
                    val newMetadataJson = json.encodeToString(newDekMetadata)
                    keyFilePath.writeText(newMetadataJson)
                    setFilePermissions(keyFilePath)
                }
            )
        } catch (e: Exception) {
            // A failure here should not fail the primary read operation.
            // The system will try again on the next read.
            logger.error("Failed to perform active DEK re-encryption", e)
        }
    }

    private fun setFilePermissions(file: File) {
        try {
            if (file.toPath().fileSystem.supportedFileAttributeViews().contains("posix")) {
                Files.setPosixFilePermissions(
                    file.toPath(),
                    PosixFilePermissions.fromString("rw-------")
                )
            }
        } catch (_: Exception) {
            // Ignore...
        }
    }
}
