package eu.torvian.chatbot.app.koin

import arrow.core.getOrElse
import eu.torvian.chatbot.app.service.auth.DesktopTokenStorage
import eu.torvian.chatbot.app.service.auth.TokenStorage
import eu.torvian.chatbot.common.security.AESCryptoProvider
import eu.torvian.chatbot.common.security.CryptoProvider
import eu.torvian.chatbot.common.security.EncryptionConfig
import org.koin.dsl.module
import java.io.File

actual fun platformModule() = module {
    single<CryptoProvider> {
        // TODO: **IMPORTANT:** Change this in production!
        //       Read from application.conf or similar
        val encryptionConfig = EncryptionConfig(
            masterKeys = mapOf(1 to "G2CgJOQQtIC+yfz+LLoDp/osBLUVzW9JE9BrQA0dQFo="),
            keyVersion = 1
        )
        AESCryptoProvider(encryptionConfig)
    }

    single<TokenStorage> {
        // TODO: **IMPORTANT:** Change this in production!
        //       Read from application.conf or similar
        val storageDir = File(System.getProperty("user.home"), ".chatbot")
        DesktopTokenStorage(
            cryptoProvider = get(),
            storageDirectoryPath = storageDir.absolutePath
        )
    }
}

/**
 * Creates a desktop-specific encryption configuration.
 * Generates a master key if none exists, otherwise loads the existing one.
 */
private fun createDesktopEncryptionConfig(): EncryptionConfig {
    val configDir = File(System.getProperty("user.home"), ".chatbot")
    val keyFile = File(configDir, "master.key")

    val masterKey = if (keyFile.exists()) {
        // Load existing key
        keyFile.readText().trim()
    } else {
        // Generate new master key
        configDir.mkdirs()
        val base64Key = AESCryptoProvider.generateRandomKey().getOrElse { error ->
            throw IllegalStateException("Failed to generate encryption key: $error")
        }

        // Save the key securely
        keyFile.writeText(base64Key)

        // Set file permissions to 600 (owner read/write only) if supported
        try {
            if (keyFile.toPath().fileSystem.supportedFileAttributeViews().contains("posix")) {
                java.nio.file.Files.setPosixFilePermissions(
                    keyFile.toPath(),
                    java.nio.file.attribute.PosixFilePermissions.fromString("rw-------")
                )
            }
        } catch (_: Exception) {
            // Ignore permission setting errors on non-POSIX systems
        }

        base64Key
    }

    return EncryptionConfig(
        masterKeys = mapOf(1 to masterKey),
        keyVersion = 1
    )
}
