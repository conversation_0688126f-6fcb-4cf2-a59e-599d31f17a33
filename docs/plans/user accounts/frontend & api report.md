# Frontend & API User Accounts Implementation - Technical Report

## Executive Summary

This report provides a comprehensive analysis of the current codebase and outlines the specific components that need modification to implement user accounts functionality in the Kotlin/Compose frontend and Ktor API layer. The investigation reveals a well-structured architecture with clear separation of concerns, but currently lacks authentication integration in the frontend layer.

This revised report specifies using the standard **Ktor `ktor-client-auth` plugin** for bearer token authentication. This idiomatic approach provides transparent token injection, automatic token refresh, and request retries, integrating cleanly with our existing `TokenStorage` and `AuthApi` components.

## Current State Analysis

### 1. Frontend Architecture (Compose/Kotlin)

#### Navigation & Routing
- **Current Structure**: Simple two-screen navigation using Jetpack Navigation Compose
  - `AppShell.kt`: Main application shell with `NavHost` and `Scaffold`
  - Routes: `Chat` (main interface) and `Settings` (configuration)
  - Navigation state managed via `rememberNavController()`

#### UI Components
- **Main Screens**:
  - `ChatScreen.kt`: Stateful wrapper for chat interface
  - `ChatScreenContent.kt`: Stateless chat content with session list and chat area
  - `SettingsScreen.kt`: Tabbed interface for providers, models, and settings

- **Key Components**:
  - `SessionListPanel.kt`: Displays user sessions and groups
  - `ChatArea.kt`: Message display and input area
  - Settings tabs: `ProvidersTabRoute.kt`, `ModelsTabRoute.kt`, `SettingsConfigTabRoute.kt`

#### State Management
- **ViewModels**: 
  - `SessionListViewModel`: Manages session and group data
  - `ChatViewModel`: Handles chat state and interactions
  - Configuration ViewModels: `ProviderConfigViewModel`, `ModelConfigViewModel`, `SettingsConfigViewModel`

- **Repositories**: 
  - `SessionRepository`, `GroupRepository`, `ModelRepository`, `ProviderRepository`, `SettingsRepository`
  - All use reactive `StateFlow<DataState<RepositoryError, T>>` pattern

#### HTTP Client Configuration
- **Current Setup**: Basic Ktor HttpClient without authentication
  - `createHttpClient.kt`: Configures JSON serialization, logging, timeouts
  - **Missing**: Authentication headers, token management, request interceptors

### 2. Backend API Layer (Ktor)

#### Authentication Infrastructure
- **Existing Components**:
  - `AuthenticationService`: Complete JWT-based authentication
  - `UserService`: User registration and management
  - `JwtConfig`: Token generation and validation
  - Authentication routes: `/api/v1/auth/*` (login, register, logout, refresh, me)

#### API Protection
- **Current State**: All data endpoints require `AuthSchemes.USER_JWT` authentication
  - Sessions: `/api/v1/sessions` - user-scoped data
  - Groups: `/api/v1/groups` - user-scoped data  
  - Messages: `/api/v1/messages` - ownership validation
  - Models/Providers/Settings: Currently global, need access control

#### Data Models
- **User Management**: Complete user entity and session management
- **Ownership**: User-scoped sessions and groups already implemented
- **Missing**: Frontend authentication models and API client integration

### 3. Dependency Injection (Koin)

#### Current Modules
- **Frontend** (`appModule`): HttpClient, repositories, ViewModels, API clients
- **Backend**: Complete DI setup with authentication services
- **Missing**: Authentication-aware frontend components

## Required Modifications

### 1. Frontend Authentication Components

#### New Components to Create
```kotlin
// Authentication screens
app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/
├── LoginScreen.kt                    // Login form UI
├── RegisterScreen.kt                 // Registration form UI
├── AuthenticationWrapper.kt          // Auth state wrapper
└── AuthLoadingScreen.kt             // Loading/splash screen

// Authentication state management
app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/
├── AuthViewModel.kt                  // Authentication state and actions
└── AuthState.kt                     // Authentication state contracts
// Token storage (unchanged from previous plan)
app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/
├── TokenStorage.kt                   // Interface with Either return types
└── TokenStorageError.kt              // Sealed error class for storage issues
```

#### Navigation Updates
```kotlin
// New routes
app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/navigation/AppRoute.kt
- Add: Login, Register, AuthLoading routes
- Implement authentication guards

// Updated AppShell
app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/AppShell.kt
- Add authentication state checking
- Implement conditional navigation (auth vs main app)
- Add logout functionality to top bar
```

### 2. HTTP Client Authentication

#### Token Management
```kotlin
// Token storage interface using Either
app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt
// Platform-specific implementations (will return Either<TokenStorageError, T>)
app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
app/src/wasmJsMain/kotlin/eu/torvian/chatbot/app/service/auth/WebTokenStorage.kt
```

#### Ktor Client Configuration (`appModule.kt`)
- **No `AuthenticatedHttpClient` wrapper class is needed.**
- Instead, the Koin module will configure two `HttpClient` instances:
    1.  An **unauthenticated client** for login, register, and token refresh calls.
    2.  An **authenticated client** that installs the standard Ktor `Auth` plugin with a `bearer` provider. This provider will be configured to use our `TokenStorage` and `AuthApi`.

#### API Client Updates
-   **No changes are required for existing API clients** like `KtorGroupApiClient`. They will simply be injected with the new authenticated `HttpClient` instance by Koin.
-   `KtorAuthApiClient` will be injected with the unauthenticated `HttpClient` to prevent circular dependencies during token refresh.

### 3. Repository Layer Updates

#### Authentication Repository
```kotlin
app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/AuthRepository.kt
- Login/logout operations
- Token refresh handling
- User profile management
- Authentication state management
- Will be responsible for triggering a global logout if the Ktor Auth plugin reports a final refresh failure.
```

#### Existing Repository Updates
- No changes required. Error handling for `401 Unauthorized` will be transparently handled by the HTTP client plugin. If a refresh fails permanently, the original `401` error will propagate up as an `ApiResourceError.ServerError`, which repositories already handle.

### 4. Koin Module Updates

#### Frontend DI Updates
```kotlin
app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
// (See detailed example in "Implementation Examples" section)
- Define Koin qualifiers: `named("AuthenticatedClient")`, `named("UnauthenticatedClient")`
- Provide `TokenStorage` (platform-specific)
- Provide `AuthApi` using the unauthenticated client
- Provide `AuthRepository`
- Configure the authenticated client with the Ktor `Auth` plugin, linking `loadTokens` and `refreshTokens` to our `TokenStorage` and `AuthApi`
- Inject the authenticated client into all data-fetching API clients (`KtorGroupApiClient`, etc.)
```

## Implementation Sequence

### Phase 1: Authentication Infrastructure
1.  **Token Management**: Implement `TokenStorage` interface and platform-specific implementations using `Either<TokenStorageError, T>`.
2.  **Authentication API Client**: Implement `KtorAuthApiClient`.
3.  **Authentication Repository**: Create `AuthRepository`.
4.  **Koin & HTTP Client Configuration**: Update `appModule.kt` to configure the Ktor `Auth` plugin with `bearer` authentication, wiring it to `TokenStorage` and `AuthApi`.

### Phase 2: Authentication UI
1. **Authentication Screens**: Implement `LoginScreen` and `RegisterScreen`
2. **Authentication ViewModel**: Create `AuthViewModel` for auth state management
3. **Navigation Updates**: Add auth routes and guards to `AppShell`
4. **Loading States**: Implement authentication loading and error states

### Phase 3: Integration & Testing
1.  **API Client Updates**: Update Koin wiring to inject the correct `HttpClient` (authenticated or unauthenticated) into each API client. **No code changes to the clients themselves.**
2.  **Testing**: Thoroughly test the refresh flow by mocking API responses and verifying the Ktor plugin's retry behavior.

## Technical Considerations

### Security
- Secure token storage using platform-appropriate mechanisms
- Automatic token refresh before expiration
- Proper logout handling (clear tokens, redirect to login)
- CSRF protection for web platform

### User Experience
- Seamless authentication flow with loading states
- Persistent login across app restarts
- Graceful handling of authentication errors
- Auto-logout on token expiration with user notification

### Error Handling
- Network connectivity issues during authentication
- Invalid credentials with clear error messages
- Token refresh failures with re-authentication flow
- API authentication errors with automatic logout

### Platform Considerations
- **Desktop**: File-based secure token storage
- **Web (WASM)**: Browser localStorage/sessionStorage with encryption
- **Future Mobile**: Platform keychain/keystore integration

## Dependencies & Integration Points

### Frontend ↔ Backend Integration
- Authentication API endpoints already implemented
- User-scoped data endpoints already protected
- JWT token format and validation already established

### State Management Integration
- Authentication state needs integration with existing reactive architecture
- User context propagation through ViewModels and repositories
- Session management coordination with backend user sessions

### Testing Strategy
- Unit tests for authentication components
- Integration tests for authenticated API calls
- UI tests for authentication flows
- Mock authentication for development and testing

## Potential Challenges

1. **Token Refresh Timing**: Coordinating token refresh across multiple concurrent API calls
2. **State Synchronization**: Ensuring authentication state consistency across components
3. **Platform Differences**: Handling token storage differences between desktop and web
4. **Migration Path**: Graceful transition from current global data to user-scoped data
5. **Error Recovery**: Robust handling of authentication failures and network issues

## Implementation Examples

### Authentication State Management
```kotlin
// AuthState.kt - Authentication state contracts
sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User, val token: String) : AuthState()
    data class Error(val message: String) : AuthState()
}

// AuthViewModel.kt - Authentication ViewModel
class AuthViewModel(
    private val authRepository: AuthRepository,
    private val tokenManager: TokenManager
) : ViewModel() {
    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    suspend fun login(username: String, password: String) {
        _authState.value = AuthState.Loading
        authRepository.login(username, password).fold(
            ifLeft = { error -> _authState.value = AuthState.Error(error.message) },
            ifRight = { loginResult ->
                tokenManager.saveTokens(loginResult.accessToken, loginResult.refreshToken, loginResult.expiresAt)
                _authState.value = AuthState.Authenticated(loginResult.user, loginResult.accessToken)
            }
        )
    }
}
```

### Koin Module with Ktor Auth Plugin
This is the central piece of the integration.
```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
package eu.torvian.chatbot.app.koin

import arrow.core.computations.either
import eu.torvian.chatbot.app.repository.*
import eu.torvian.chatbot.app.repository.impl.DefaultAuthRepository
// ... other repository imports
import eu.torvian.chatbot.app.service.api.*
import eu.torvian.chatbot.app.service.api.ktor.*
import eu.torvian.chatbot.app.service.auth.TokenStorage
import eu.torvian.chatbot.app.service.auth.DesktopTokenStorage // or WebTokenStorage
import eu.torvian.chatbot.common.models.auth.RefreshTokenRequest
// ... other imports
import io.ktor.client.*
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

val UnauthenticatedClient = named("UnauthenticatedClient")
val AuthenticatedClient = named("AuthenticatedClient")

fun appModule(baseUri: String): Module = module {
    // ... other single/viewModel definitions ...

    // --- Authentication Infrastructure ---
    single<TokenStorage> { DesktopTokenStorage() /* or WebTokenStorage() */ }
    single<AuthApi> { KtorAuthApiClient(get(UnauthenticatedClient)) }
    single<AuthRepository> { DefaultAuthRepository(get(), get(), get()) }

    // --- HttpClient Definitions ---

    // 1. Plain HttpClient for unauthenticated calls (login, register, refresh)
    single(UnauthenticatedClient) { createHttpClient(baseUri, Json) }

    // 2. HttpClient with the standard Ktor Auth plugin installed
    single(AuthenticatedClient) {
        val unauthenticatedClient = get<HttpClient>(UnauthenticatedClient)
        val tokenStorage = get<TokenStorage>()
        val authApi = get<AuthApi>()
        val authRepository = get<AuthRepository>() // For triggering logout on catastrophic failure

        unauthenticatedClient.config {
            install(Auth) {
                bearer {
                    loadTokens {
                        // Bridge our Either-based storage to the plugin's nullable BearerTokens
                        val result = either<Any, BearerTokens> {
                            val accessToken = tokenStorage.getAccessToken().bind()
                            val refreshToken = tokenStorage.getRefreshToken().bind()
                            BearerTokens(accessToken, refreshToken)
                        }
                        result.getOrNull()
                    }

                    refreshTokens {
                        val oldRefreshToken = oldTokens?.refreshToken ?: run {
                            // No refresh token available, logout and fail
                            authRepository.logout()
                            return@refreshTokens null
                        }

                        // Use AuthApi (with unauthenticated client) to refresh
                        authApi.refreshToken(RefreshTokenRequest(oldRefreshToken)).fold(
                            ifLeft = { apiError ->
                                // Refresh failed (e.g., token expired/revoked).
                                // Clear local tokens and trigger a global logout.
                                authRepository.logout()
                                null // Return null to signify refresh failure
                            },
                            ifRight = { loginResponse ->
                                // Refresh successful. Save the new tokens.
                                tokenStorage.saveTokens(
                                    loginResponse.accessToken,
                                    loginResponse.refreshToken,
                                    loginResponse.expiresAt
                                )
                                // Return the new tokens to the plugin
                                BearerTokens(loginResponse.accessToken, loginResponse.refreshToken)
                            }
                        )
                    }

                    sendWithoutRequest { request ->
                        // Don't send tokens to the auth endpoints themselves
                        !request.url.encodedPath.contains("/api/v1/auth/")
                    }
                }
            }
        }
    }

    // --- API Client Wiring ---
    single<GroupApi> { KtorGroupApiClient(get(AuthenticatedClient)) }
    single<SessionApi> { KtorSessionApiClient(get(AuthenticatedClient)) }
    // ... and so on for all other data APIs
}
```

### Navigation with Authentication Guards
```kotlin
// AppShell.kt - Updated with authentication flow
@Composable
fun AppShell() {
    val authViewModel: AuthViewModel = koinViewModel()
    val authState by authViewModel.authState.collectAsState()

    when (authState) {
        is AuthState.Loading -> AuthLoadingScreen()
        is AuthState.Unauthenticated -> AuthenticationFlow()
        is AuthState.Authenticated -> MainApplicationFlow(authState.user)
        is AuthState.Error -> AuthErrorScreen(authState.message) {
            authViewModel.retry()
        }
    }
}

@Composable
private fun AuthenticationFlow() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = Login) {
        composable<Login> { LoginScreen(navController) }
        composable<Register> { RegisterScreen(navController) }
    }
}

@Composable
private fun MainApplicationFlow(user: User) {
    // Existing main app navigation with user context
    val navController = rememberNavController()
    // ... existing navigation setup with user info in top bar
}
```

## Conclusion
The codebase is well-positioned for user accounts implementation. The main work involves creating the authentication UI, implementing the `TokenStorage` and `AuthRepository`, and correctly configuring the standard **Ktor `Auth` plugin**.

This approach is superior as it leverages a well-tested, idiomatic Ktor feature, requires **zero changes to existing API client implementations**, and cleanly separates the authentication mechanism from the business logic of the API calls. It fully supports our `Either`-based error handling paradigm by integrating our `TokenStorage` within the plugin's `loadTokens` and `refreshTokens` lifecycle hooks.
