# Phase 1: Authentication Infrastructure - Development Plan (Revised)
## Overview
This plan details the implementation of Phase 1 of the user accounts feature, focusing on building the core authentication infrastructure for the Kotlin/Compose chatbot application.

Primary change in this revision: the TokenStorage interface and all related components use Arrow's Either for errors instead of throwing exceptions / returning nullable values. Token storage operations now return Either<TokenStorageError, T>, keeping error handling consistent with existing ApiResourceError / RepositoryError patterns.

## Task Breakdown
### Task 1: Token Management Infrastructure
#### 1.1 Platform-Agnostic Token Storage Interface
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorageError.kt`

**Interface (revised):**
```kotlin
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import kotlinx.datetime.Instant

/**
 * Token storage interface returning Arrow Either for errors.
 */
interface TokenStorage {
    suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit>

    suspend fun getAccessToken(): Either<TokenStorageError, String>

    suspend fun getRefreshToken(): Either<TokenStorageError, String>

    suspend fun getExpiry(): Either<TokenStorageError, Instant>

    suspend fun clearTokens(): Either<TokenStorageError, Unit>

    suspend fun isTokenExpired(token: String): Either<TokenStorageError, Boolean>
}
```

**TokenStorageError (new sealed error type):**
```kotlin
package eu.torvian.chatbot.app.service.auth

sealed class TokenStorageError {
    abstract val message: String
    abstract val cause: Throwable?

    data class IOError(override val message: String, override val cause: Throwable? = null) : TokenStorageError()
    data class EncryptionError(override val message: String, override val cause: Throwable? = null) : TokenStorageError()
    data class NotFound(override val message: String = "Token not found") : TokenStorageError() { override val cause: Throwable? = null }
    data class InvalidTokenFormat(override val message: String, override val cause: Throwable? = null) : TokenStorageError()
    data class Unknown(override val message: String, override val cause: Throwable? = null) : TokenStorageError()
}
```

**Deliverables:**
- Interface and error type definitions with KDoc
- Clear contract: all implementations map errors to TokenStorageError and return Either
- Token expiry-related helpers (parsing JWT expiry or storing expiresAt)

**Acceptance Criteria:**
- [ ] Interface compiles and follows existing codebase patterns
- [ ] All methods are properly documented with KDoc
- [ ] Methods return Either<TokenStorageError, T> for success/error
- [ ] Token expiration logic available (either via stored expiry or JWT claims)
- [ ] Unit tests validate both Left and Right flows

#### 1.2 Desktop Token Storage Implementation
**Files to Create:**
- `app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt`

**Implementation notes:**
- Use `Either` on all public methods. Map IO/encryption exceptions to `TokenStorageError.IOError` or `EncryptionError`.
- Token file: `File(System.getProperty("user.home"), ".chatbot/tokens.enc")`
- Encrypt before writing, decrypt on read.
- Set file permissions to 600.
- Provide graceful error reporting via `Either.Left(TokenStorageError.*)`.

**Example signature:**
```kotlin
class DesktopTokenStorage(...) : TokenStorage {
    override suspend fun saveTokens(...): Either<TokenStorageError, Unit> = ...
    override suspend fun getAccessToken(): Either<TokenStorageError, String> = ...
    // ...
}
```

**Acceptance Criteria:**
- [ ] Tokens are encrypted before storage
- [ ] File permissions set appropriately (600)
- [ ] IO and encryption failures returned as TokenStorageError.Left
- [ ] Unit tests cover success and failure cases

#### 1.3 Web Token Storage Implementation
**Files to Create:**
- `app/src/wasmJsMain/kotlin/eu/torvian/chatbot/app/service/auth/WebTokenStorage.kt`

**Implementation notes:**
- Use `localStorage` (or `sessionStorage` fallback) but wrap operations in `Either`.
- Encrypt tokens before storing. Map browser/storage exceptions to `TokenStorageError.IOError` or `EncryptionError`.
- Handle quota errors and return `TokenStorageError.IOError` with meaningful message.

**Acceptance Criteria:**
- [ ] Tokens encrypted before localStorage storage
- [ ] localStorage quota and availability errors mapped to TokenStorageError.Left
- [ ] Fallback to sessionStorage when appropriate
- [ ] Unit tests (using platform mocks) cover success and failure flows

**Estimated Effort:** 2-3 days  
**Dependencies:** None  
**Risks:** Platform-specific encryption complexity

---
### Task 2: Authenticated HTTP Client
#### 2.1 Authentication-Aware HTTP Client (updated to handle Either)
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/AuthenticatedHttpClient.kt`

**Implementation highlights:**
- Interact with `TokenStorage` which returns Either. Compose logic must handle Left (error) and Right (token).
- Use Arrow's combinators or manual pattern matching to chain operations.
- On token retrieval failure (Left), decide whether to surface repository-level error or attempt refresh (depending on error type).
- `authenticatedRequest` should return `Either<ApiResourceError, T>` to remain compatible with `BaseApiResourceClient` usage patterns.

**Example scaffold:**
```kotlin
class AuthenticatedHttpClient(
    private val baseClient: HttpClient,
    private val tokenStorage: TokenStorage,
    private val authApi: AuthApi, // to call refresh
    private val json: Json,
    private val logger: KmpLogger = kmpLogger<AuthenticatedHttpClient>()
) {
    suspend fun <T> authenticatedRequest(
        block: suspend HttpClient.() -> T
    ): Either<ApiResourceError, T> {
        // 1) get access token (Either)
        val accessEither = tokenStorage.getAccessToken()
        return accessEither.fold(
            { tokenErr ->
                // token retrieval failed - map to ApiResourceError.UnknownError or attempt refresh based on tokenErr type
                ApiResourceError.UnknownError("Token storage error: ${tokenErr.message}", tokenErr.cause).left()
            },
            { token ->
                // 2) if token not expired -> execute with Authorization header
                tokenStorage.isTokenExpired(token).fold(
                    { isExpiredErr -> ApiResourceError.UnknownError("Token expiry check failed: ${isExpiredErr.message}", isExpiredErr.cause).left() },
                    { expired ->
                        if (!expired) {
                            executeWithAuth(token, block)
                        } else {
                            // Attempt refresh flow and retry - refreshToken also from TokenStorage (Either)
                            refreshTokenAndRetry(block)
                        }
                    }
                )
            }
        )
    }

    private suspend fun <T> executeWithAuth(token: String, block: suspend HttpClient.() -> T): Either<ApiResourceError, T> {
        // Add Authorization header to baseClient and run block using safeApiCall / BaseApiResourceClient patterns
    }

    private suspend fun <T> refreshTokenAndRetry(block: suspend HttpClient.() -> T): Either<ApiResourceError, T> {
        // Retrieve refresh token (Either)
        // Call authApi.refreshToken(...)
        // On success, saveTokens(...) via TokenStorage.saveTokens(...) which returns Either
        // On save success, retry executeWithAuth(...)
        // Map token storage or network errors to ApiResourceError
    }
}
```

**Key Features:**
- Automatic Authorization header injection
- Token refresh flow using `TokenStorage` and `AuthApi`
- Proper mapping from TokenStorageError -> ApiResourceError
- Automatic logout / invalidation on unrecoverable auth failures

**Acceptance Criteria:**
- [ ] Authorization header added automatically when valid token present
- [ ] Token refresh invoked when access token expired
- [ ] TokenStorage Left values are properly translated into ApiResourceError.Left
- [ ] 401 responses trigger refresh or high-level logout as appropriate
- [ ] Integrates seamlessly with existing API clients (returns Either<ApiResourceError, T>)
- [ ] Unit tests cover both Left/Right flows and refresh race conditions

#### 2.2 HTTP Client Factory Updates
**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt`

**Changes:**
- Provide factory methods for both plain `HttpClient` and for wrapping with `AuthenticatedHttpClient`.
- Do not break existing clients: still create the plain client as before; new DI wiring can choose to inject AuthenticatedHttpClient or plain client.

**Acceptance Criteria:**
- [ ] Factory method for creating `AuthenticatedHttpClient` available
- [ ] Existing factory method remains unchanged
- [ ] No breaking changes to existing API clients

**Estimated Effort:** 3-4 days  
**Dependencies:** Task 1 (TokenStorage)  
**Risks:** Integration complexity with existing HTTP client configuration

---
### Task 3: Authentication API Client
#### 3.1 Authentication API Client Implementation
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/AuthApi.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorAuthApiClient.kt`

**Notes:**
- Keep current `Either<ApiResourceError, T>` return style.
- `refreshToken` endpoint will be used by `AuthenticatedHttpClient` for refreshing.

**Acceptance Criteria:**
- [ ] All auth endpoints implemented
- [ ] Integration tests for refresh flow and error mapping

**Estimated Effort:** 2 days  
**Dependencies:** None  
**Risks:** Low

---
### Task 4: Authentication Repository
#### 4.1 Authentication Repository Implementation
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/AuthRepository.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/impl/DefaultAuthRepository.kt`

**Interface summary (unchanged high-level, but implementations will use TokenStorage Either flows):**
- `val authState: StateFlow<AuthState>`
- `suspend fun login(...) : Either<RepositoryError, LoginResult>`
- `suspend fun register(...) : Either<RepositoryError, User>`
- `suspend fun logout() : Either<RepositoryError, Unit>`
- `suspend fun refreshToken() : Either<RepositoryError, LoginResult>`
- `suspend fun getCurrentUser() : Either<RepositoryError, User>`
- `suspend fun checkAuthenticationStatus() : Either<RepositoryError, AuthState>`

**Implementation notes:**
- Internally call AuthApi and persist tokens via `TokenStorage.saveTokens(...)` which returns Either; map TokenStorageError into RepositoryError.OtherError or a specific RepositoryError variant.
- `authState` should reflect Left/Right outcomes consistently.

**Acceptance Criteria:**
- [ ] Reactive auth state using StateFlow
- [ ] All token-persistence operations map TokenStorageError -> RepositoryError
- [ ] Unit tests validate error propagation (TokenStorage Left -> RepositoryError.Left)

**Estimated Effort:** 3 days  
**Dependencies:** Task 1 (TokenStorage), Task 3 (AuthApi)  
**Risks:** State management complexity

---
## Implementation Order & Dependencies
```
graph TD
    A[Task 1.1: TokenStorage Interface] --> B[Task 1.2: Desktop TokenStorage]
    A --> C[Task 1.3: Web TokenStorage]
    B --> D[Task 2.1: AuthenticatedHttpClient]
    C --> D
    D --> E[Task 2.2: HTTP Client Factory Updates]
    F[Task 3.1: AuthApi Client] --> G[Task 4.1: AuthRepository]
    A --> G
    E --> H[Integration & Testing]
    G --> H
```

Recommended sequence remains the same, but implementations must respect Either return types so Task 1 should complete before Task 2/4 progress.

---
## Testing Strategy (updated)
### Unit Tests
**Token Storage Tests:**
- `TokenStorageTest.kt` must assert both Right and Left cases:
    - Successful save & retrieval returns Right(...)
    - IO/encryption errors return Left(TokenStorageError.IOError/EncryptionError)
    - getAccessToken on missing tokens returns Left(TokenStorageError.NotFound)

**Authenticated HTTP Client Tests:**
- Ensure the client properly:
    - Reads tokens (Right) and injects header
    - Handles TokenStorage Left by propagating ApiResourceError.Left
    - Calls refresh flow when token expired and persists new tokens (mock TokenStorage & AuthApi to return Either)

**Repository Tests:**
- Mock TokenStorage to return Left and ensure repository surfaces appropriate RepositoryError.Left types.

### Integration Tests
- End-to-end login -> token persist flow (backend test server + real TokenStorage on test platform)
- Token refresh and retry behavior with simulated expired token and successful refresh
- Failure modes: TokenStorage IO failure causes auth operations to fail gracefully

---
## Integration Points
### Koin Dependency Injection Updates (revised)
**Module changes in `appModule`:**
```kotlin
fun appModule(baseUri: String): Module = module {
    // Token Storage (platform-specific)
    single<TokenStorage> {
        if (Platform.isDesktop) DesktopTokenStorage() else WebTokenStorage()
    }

    // Provide plain HttpClient
    single<HttpClient> { createHttpClient(baseUri, Json) }

    // Provide AuthenticatedHttpClient which depends on TokenStorage and AuthApi
    single {
        val httpClient: HttpClient = get()
        val tokenStorage: TokenStorage = get()
        val authApi = KtorAuthApiClient(httpClient)
        AuthenticatedHttpClient(httpClient, tokenStorage, authApi, Json)
    }

    // Provide AuthApi (uses plain HttpClient or authenticated client as appropriate)
    single<AuthApi> { KtorAuthApiClient(get<HttpClient>()) }

    // AuthRepository
    single<AuthRepository> { DefaultAuthRepository(get(), get(), get()) }
    // ...
}
```
- Ensure only one canonical TokenStorage is bound and that all consumers use Either-based APIs.

---
## Acceptance Criteria Summary (updated)
### Task 1: Token Management
- [ ] TokenStorage interface returns Either<TokenStorageError, T>
- [ ] Desktop & Web implementations return Left on failures and Right on success
- [ ] JWT expiry validation available
- [ ] Unit tests for Left/Right cases

### Task 2: Authenticated HTTP Client
- [ ] Inject Authorization header when token present and valid
- [ ] Trigger refresh flow when expired; persist resulting tokens via TokenStorage (Either)
- [ ] Translate TokenStorage errors into ApiResourceError.Left
- [ ] Unit tests for token retrieval, expiry, refresh and error flows

### Task 3: Authentication API Client
- [ ] All auth endpoints implemented and return Either<ApiResourceError, T]

### Task 4: Authentication Repository
- [ ] AuthRepository properly integrates TokenStorage Either flows and updates authState
- [ ] TokenStorage errors surface as RepositoryError variants

### Overall Integration
- [ ] Koin DI updated to provide TokenStorage and AuthenticatedHttpClient
- [ ] No breaking changes to existing code (existing API clients still usable)
- [ ] Test coverage includes Left/Right paths for token storage and auth flows

---
## Estimated Effort & Risks (unchanged totals)
### Total Estimated Effort: 12-15 days

### Potential Risks & Mitigation
- Platform-specific encryption complexity — mitigate by starting with well-known libraries and clear mapping to TokenStorageError
- Token refresh race conditions — mitigate by synchronizing refresh logic inside AuthenticatedHttpClient (single inflight refresh)
- Integration complexity — mitigate by keeping plain HttpClient factory and introducing AuthenticatedHttpClient as opt-in

## Success Metrics
- [ ] Unit and integration tests cover both success and failure (Either.Left) flows
- [ ] Auth flows handle token storage failures gracefully and consistently
- [ ] No unexpected exceptions escaping into UI; all errors mapped to Either for upstream handling

---
## Notes & Implementation Hints
- Use Arrow's `Either` combinators (fold, map, flatMap) to handle token storage flows cleanly.
- Map TokenStorageError to ApiResourceError in `AuthenticatedHttpClient` when surfacing auth-related failures to API clients.
- Keep TokenStorage responsibilities limited: persist / retrieve / clear / validate. Business logic (when to refresh, what to do on failure) belongs in AuthRepository or AuthenticatedHttpClient.
- Consider a small adapter helper for converting TokenStorageError -> ApiResourceError / RepositoryError to avoid code duplication.

This revised plan keeps the original structure while ensuring the new TokenStorage contract is consistent with the project's error-handling approach using Arrow's Either.

