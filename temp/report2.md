# Frontend & API User Accounts Implementation - Technical Report

## Executive Summary

This report provides a comprehensive analysis of the current codebase and outlines the specific components that need modification to implement user accounts functionality in the Kotlin/Compose frontend and Ktor API layer. The investigation reveals a well-structured architecture with clear separation of concerns, but currently lacks authentication integration in the frontend layer.

## Current State Analysis

### 1. Frontend Architecture (Compose/Kotlin)

#### Navigation & Routing
- **Current Structure**: Simple two-screen navigation using Jetpack Navigation Compose
  - `AppShell.kt`: Main application shell with `NavHost` and `Scaffold`
  - Routes: `Chat` (main interface) and `Settings` (configuration)
  - Navigation state managed via `rememberNavController()`

#### UI Components
- **Main Screens**:
  - `ChatScreen.kt`: Stateful wrapper for chat interface
  - `ChatScreenContent.kt`: Stateless chat content with session list and chat area
  - `SettingsScreen.kt`: Tabbed interface for providers, models, and settings

- **Key Components**:
  - `SessionListPanel.kt`: Displays user sessions and groups
  - `ChatArea.kt`: Message display and input area
  - Settings tabs: `ProvidersTabRoute.kt`, `ModelsTabRoute.kt`, `SettingsConfigTabRoute.kt`

#### State Management
- **ViewModels**: 
  - `SessionListViewModel`: Manages session and group data
  - `ChatViewModel`: Handles chat state and interactions
  - Configuration ViewModels: `ProviderConfigViewModel`, `ModelConfigViewModel`, `SettingsConfigViewModel`

- **Repositories**: 
  - `SessionRepository`, `GroupRepository`, `ModelRepository`, `ProviderRepository`, `SettingsRepository`
  - All use reactive `StateFlow<DataState<RepositoryError, T>>` pattern

#### HTTP Client Configuration
- **Current Setup**: Basic Ktor HttpClient without authentication
  - `createHttpClient.kt`: Configures JSON serialization, logging, timeouts
  - **Missing**: Authentication headers, token management, request interceptors

### 2. Backend API Layer (Ktor)

#### Authentication Infrastructure
- **Existing Components**:
  - `AuthenticationService`: Complete JWT-based authentication
  - `UserService`: User registration and management
  - `JwtConfig`: Token generation and validation
  - Authentication routes: `/api/v1/auth/*` (login, register, logout, refresh, me)

#### API Protection
- **Current State**: All data endpoints require `AuthSchemes.USER_JWT` authentication
  - Sessions: `/api/v1/sessions` - user-scoped data
  - Groups: `/api/v1/groups` - user-scoped data  
  - Messages: `/api/v1/messages` - ownership validation
  - Models/Providers/Settings: Currently global, need access control

#### Data Models
- **User Management**: Complete user entity and session management
- **Ownership**: User-scoped sessions and groups already implemented
- **Missing**: Frontend authentication models and API client integration

### 3. Dependency Injection (Koin)

#### Current Modules
- **Frontend** (`appModule`): HttpClient, repositories, ViewModels, API clients
- **Backend**: Complete DI setup with authentication services
- **Missing**: Authentication-aware frontend components

## Required Modifications

### 1. Frontend Authentication Components

#### New Components to Create
```kotlin
// Authentication screens
app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/
├── LoginScreen.kt                    // Login form UI
├── RegisterScreen.kt                 // Registration form UI
├── AuthenticationWrapper.kt          // Auth state wrapper
└── AuthLoadingScreen.kt             // Loading/splash screen

// Authentication state management
app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/
├── AuthViewModel.kt                  // Authentication state and actions
└── AuthState.kt                     // Authentication state contracts

// Authentication services
app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/
├── AuthService.kt                   // Authentication operations
├── TokenManager.kt                  // Token storage and management
└── AuthenticatedHttpClient.kt       // HTTP client with auth headers
```

#### Navigation Updates
```kotlin
// New routes
app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/navigation/AppRoute.kt
- Add: Login, Register, AuthLoading routes
- Implement authentication guards

// Updated AppShell
app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/AppShell.kt
- Add authentication state checking
- Implement conditional navigation (auth vs main app)
- Add logout functionality to top bar
```

### 2. HTTP Client Authentication

#### Token Management
```kotlin
// Token storage (platform-specific implementations)
app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt
app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
app/src/wasmJsMain/kotlin/eu/torvian/chatbot/app/service/auth/WebTokenStorage.kt

// Authenticated HTTP client
app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/AuthenticatedHttpClient.kt
- Add Authorization header interceptor
- Handle token refresh logic
- Implement logout on 401 responses
```

#### API Client Updates
```kotlin
// All existing API clients need authentication integration:
- KtorSessionApiClient.kt
- KtorGroupApiClient.kt  
- KtorModelApiClient.kt
- KtorProviderApiClient.kt
- KtorSettingsApiClient.kt
- KtorChatApiClient.kt

// New authentication API client
- KtorAuthApiClient.kt (login, register, refresh, logout)
```

### 3. Repository Layer Updates

#### Authentication Repository
```kotlin
app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/AuthRepository.kt
- Login/logout operations
- Token refresh handling
- User profile management
- Authentication state management
```

#### Existing Repository Updates
- All repositories need authentication error handling
- Implement automatic logout on authentication failures
- Add user context to error reporting

### 4. Koin Module Updates

#### Frontend DI Updates
```kotlin
app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
- Add AuthService, TokenManager, AuthRepository
- Replace HttpClient with AuthenticatedHttpClient
- Add AuthViewModel and related components
- Add platform-specific TokenStorage implementations
```

## Implementation Sequence

### Phase 1: Authentication Infrastructure
1. **Token Management**: Implement `TokenStorage` with platform-specific implementations
2. **Authenticated HTTP Client**: Create `AuthenticatedHttpClient` with token injection
3. **Authentication API Client**: Implement `KtorAuthApiClient` for auth endpoints
4. **Authentication Repository**: Create `AuthRepository` for auth state management

### Phase 2: Authentication UI
1. **Authentication Screens**: Implement `LoginScreen` and `RegisterScreen`
2. **Authentication ViewModel**: Create `AuthViewModel` for auth state management
3. **Navigation Updates**: Add auth routes and guards to `AppShell`
4. **Loading States**: Implement authentication loading and error states

### Phase 3: Integration & Testing
1. **Repository Updates**: Update all repositories with auth error handling
2. **API Client Updates**: Integrate authentication into all existing API clients
3. **Koin Module Updates**: Wire up all authentication components
4. **User Context**: Add user information display to UI

## Technical Considerations

### Security
- Secure token storage using platform-appropriate mechanisms
- Automatic token refresh before expiration
- Proper logout handling (clear tokens, redirect to login)
- CSRF protection for web platform

### User Experience
- Seamless authentication flow with loading states
- Persistent login across app restarts
- Graceful handling of authentication errors
- Auto-logout on token expiration with user notification

### Error Handling
- Network connectivity issues during authentication
- Invalid credentials with clear error messages
- Token refresh failures with re-authentication flow
- API authentication errors with automatic logout

### Platform Considerations
- **Desktop**: File-based secure token storage
- **Web (WASM)**: Browser localStorage/sessionStorage with encryption
- **Future Mobile**: Platform keychain/keystore integration

## Dependencies & Integration Points

### Frontend ↔ Backend Integration
- Authentication API endpoints already implemented
- User-scoped data endpoints already protected
- JWT token format and validation already established

### State Management Integration
- Authentication state needs integration with existing reactive architecture
- User context propagation through ViewModels and repositories
- Session management coordination with backend user sessions

### Testing Strategy
- Unit tests for authentication components
- Integration tests for authenticated API calls
- UI tests for authentication flows
- Mock authentication for development and testing

## Potential Challenges

1. **Token Refresh Timing**: Coordinating token refresh across multiple concurrent API calls
2. **State Synchronization**: Ensuring authentication state consistency across components
3. **Platform Differences**: Handling token storage differences between desktop and web
4. **Migration Path**: Graceful transition from current global data to user-scoped data
5. **Error Recovery**: Robust handling of authentication failures and network issues

## Implementation Examples

### Authentication State Management
```kotlin
// AuthState.kt - Authentication state contracts
sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User, val token: String) : AuthState()
    data class Error(val message: String) : AuthState()
}

// AuthViewModel.kt - Authentication ViewModel
class AuthViewModel(
    private val authRepository: AuthRepository,
    private val tokenManager: TokenManager
) : ViewModel() {
    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    suspend fun login(username: String, password: String) {
        _authState.value = AuthState.Loading
        authRepository.login(username, password).fold(
            ifLeft = { error -> _authState.value = AuthState.Error(error.message) },
            ifRight = { loginResult ->
                tokenManager.saveTokens(loginResult.accessToken, loginResult.refreshToken)
                _authState.value = AuthState.Authenticated(loginResult.user, loginResult.accessToken)
            }
        )
    }
}
```

### Authenticated HTTP Client
```kotlin
// AuthenticatedHttpClient.kt - HTTP client with automatic token injection
class AuthenticatedHttpClient(
    private val baseClient: HttpClient,
    private val tokenManager: TokenManager,
    private val authRepository: AuthRepository
) {
    suspend fun <T> authenticatedRequest(
        block: suspend HttpClient.() -> T
    ): Either<ApiResourceError, T> {
        return try {
            val token = tokenManager.getAccessToken()
            if (token != null && !tokenManager.isTokenExpired(token)) {
                // Add Authorization header and make request
                val response = baseClient.config {
                    defaultRequest {
                        header("Authorization", "Bearer $token")
                    }
                }.block()
                Either.Right(response)
            } else {
                // Try to refresh token
                refreshTokenAndRetry { baseClient.block() }
            }
        } catch (e: ClientRequestException) {
            if (e.response.status == HttpStatusCode.Unauthorized) {
                // Token invalid, logout user
                authRepository.logout()
                Either.Left(ApiResourceError.Unauthorized)
            } else {
                Either.Left(ApiResourceError.fromException(e))
            }
        }
    }
}
```

### Navigation with Authentication Guards
```kotlin
// AppShell.kt - Updated with authentication flow
@Composable
fun AppShell() {
    val authViewModel: AuthViewModel = koinViewModel()
    val authState by authViewModel.authState.collectAsState()

    when (authState) {
        is AuthState.Loading -> AuthLoadingScreen()
        is AuthState.Unauthenticated -> AuthenticationFlow()
        is AuthState.Authenticated -> MainApplicationFlow(authState.user)
        is AuthState.Error -> AuthErrorScreen(authState.message) {
            authViewModel.retry()
        }
    }
}

@Composable
private fun AuthenticationFlow() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = Login) {
        composable<Login> { LoginScreen(navController) }
        composable<Register> { RegisterScreen(navController) }
    }
}

@Composable
private fun MainApplicationFlow(user: User) {
    // Existing main app navigation with user context
    val navController = rememberNavController()
    // ... existing navigation setup with user info in top bar
}
```

## Conclusion

The codebase is well-positioned for user accounts implementation with a solid foundation of authentication services on the backend and a clean, reactive architecture on the frontend. The main work involves creating authentication UI components, integrating token management into the HTTP client layer, and updating the navigation flow to support authenticated and unauthenticated states.

The modular architecture and dependency injection setup will facilitate a clean implementation with good separation of concerns and testability. The existing reactive patterns using StateFlow and the repository architecture will integrate seamlessly with authentication state management.

Key success factors:
- Leverage existing backend authentication infrastructure
- Maintain consistency with current reactive architecture patterns
- Implement robust error handling and token refresh logic
- Ensure platform-appropriate secure token storage
- Provide smooth user experience with proper loading states
