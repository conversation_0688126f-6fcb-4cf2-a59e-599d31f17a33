### USER
We currently have this code:

```
// file: app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermissions
import java.security.SecureRandom
import java.util.*
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * Desktop implementation of [TokenStorage] using encrypted file storage.
 *
 * Tokens are stored in an encrypted file at `~/.chatbot/tokens.enc` with
 * file permissions set to 600 (owner read/write only).
 */
open class DesktopTokenStorage(
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TokenStorage {

    companion object {
        private const val ALGORITHM = "AES"
        private const val TRANSFORMATION = "AES/CBC/PKCS5Padding"
        private const val KEY_SIZE_BITS = 256
        private const val IV_SIZE = 16
    }

    @Serializable
    private data class TokenData(
        val accessToken: String,
        val refreshToken: String,
        val expiresAt: Long // Instant as epoch seconds
    )

    // Make file paths overridable for testing
    protected open val tokenFilePath: File = File(System.getProperty("user.home"), ".chatbot/tokens.enc")
    protected open val keyFilePath: File = File(System.getProperty("user.home"), ".chatbot/key.dat")

    private val encryptionKey: SecretKeySpec by lazy {
        // Generate or retrieve a consistent key for this user
        val keyBytes = if (keyFilePath.exists()) {
            keyFilePath.readBytes()
        } else {
            // Generate new key
            val keyGenerator = KeyGenerator.getInstance(ALGORITHM)
            keyGenerator.init(KEY_SIZE_BITS)
            val key = keyGenerator.generateKey().encoded

            // Create directory and save key
            keyFilePath.parentFile?.mkdirs()
            keyFilePath.writeBytes(key)
            setFilePermissions(keyFilePath)
            key
        }
        SecretKeySpec(keyBytes, ALGORITHM)
    }

    override suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit> {
        return try {
            val tokenData = TokenData(
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresAt = expiresAt.epochSeconds
            )

            val jsonString = json.encodeToString(tokenData)
            val encryptedData = encrypt(jsonString)

            // Ensure directory exists
            tokenFilePath.parentFile?.mkdirs()
            tokenFilePath.writeText(encryptedData)
            setFilePermissions(tokenFilePath)

            Unit.right()
        } catch (e: Exception) {
            when {
                e.message?.contains("encrypt", ignoreCase = true) == true ->
                    TokenStorageError.EncryptionError("Failed to encrypt token data", e).left()

                else ->
                    TokenStorageError.IOError("Failed to save tokens to file", e).left()
            }
        }
    }

    override suspend fun getAccessToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.accessToken }
    }

    override suspend fun getRefreshToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.refreshToken }
    }

    override suspend fun getExpiry(): Either<TokenStorageError, Instant> {
        return getTokenData().map { Instant.fromEpochSeconds(it.expiresAt) }
    }

    override suspend fun clearTokens(): Either<TokenStorageError, Unit> {
        return try {
            if (tokenFilePath.exists()) {
                tokenFilePath.delete()
            }
            Unit.right()
        } catch (e: Exception) {
            TokenStorageError.IOError("Failed to clear tokens", e).left()
        }
    }

    private fun getTokenData(): Either<TokenStorageError, TokenData> {
        return try {
            if (!tokenFilePath.exists()) {
                return TokenStorageError.NotFound("No tokens stored").left()
            }

            val encryptedData = tokenFilePath.readText()
            val decryptedJson = decrypt(encryptedData)
            val tokenData = json.decodeFromString<TokenData>(decryptedJson)

            tokenData.right()
        } catch (e: Exception) {
            when {
                e.message?.contains("decrypt", ignoreCase = true) == true ||
                        e.message?.contains("cipher", ignoreCase = true) == true ->
                    TokenStorageError.EncryptionError("Failed to decrypt token data", e).left()

                e.message?.contains("serialization", ignoreCase = true) == true ->
                    TokenStorageError.InvalidTokenFormat("Failed to parse stored token data", e).left()

                else ->
                    TokenStorageError.IOError("Failed to read tokens from file", e).left()
            }
        }
    }

    private fun encrypt(plainText: String): String {
        val cipher = Cipher.getInstance(TRANSFORMATION)
        val iv = ByteArray(IV_SIZE).also { SecureRandom().nextBytes(it) }
        cipher.init(Cipher.ENCRYPT_MODE, encryptionKey, IvParameterSpec(iv))
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        val combined = iv + encryptedBytes
        return Base64.getEncoder().encodeToString(combined)
    }

    private fun decrypt(cipherText: String): String {
        val combined = Base64.getDecoder().decode(cipherText)
        if (combined.size < IV_SIZE) {
            throw IllegalArgumentException("Ciphertext is too short to contain IV")
        }

        val iv = combined.copyOfRange(0, IV_SIZE)
        val encryptedBytes = combined.copyOfRange(IV_SIZE, combined.size)

        val cipher = Cipher.getInstance(TRANSFORMATION)
        cipher.init(Cipher.DECRYPT_MODE, encryptionKey, IvParameterSpec(iv))
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }

    private fun setFilePermissions(file: File) {
        try {
            // Set file permissions to 600 (owner read/write only) on Unix-like systems
            if (file.toPath().fileSystem.supportedFileAttributeViews().contains("posix")) {
                Files.setPosixFilePermissions(
                    file.toPath(),
                    PosixFilePermissions.fromString("rw-------")
                )
            }
        } catch (_: Exception) {
            // Ignore permission setting errors on non-POSIX systems (e.g., Windows)
            // The file is still protected by the OS user account permissions
        }
    }
}


// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import kotlinx.datetime.Instant

/**
 * Platform-agnostic interface for secure token storage operations.
 *
 * This interface provides a contract for storing and retrieving authentication tokens
 * across different platforms (desktop, web). All operations return Arrow's Either
 * type to provide consistent error handling without exceptions.
 *
 * Implementations should:
 * - Encrypt tokens before storage for security
 * - Handle platform-specific storage mechanisms (files, localStorage, etc.)
 * - Map all storage errors to appropriate [TokenStorageError] types
 * - Provide thread-safe operations where applicable
 */
interface TokenStorage {

    /**
     * Saves authentication tokens to secure storage.
     *
     * @param accessToken The JWT access token for API authentication
     * @param refreshToken The refresh token for obtaining new access tokens
     * @param expiresAt The expiration timestamp for the access token
     * @return Either a [TokenStorageError] on failure or Unit on success
     */
    suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit>

    /**
     * Retrieves the stored access token.
     *
     * @return Either a [TokenStorageError] on failure or the access token on success
     */
    suspend fun getAccessToken(): Either<TokenStorageError, String>

    /**
     * Retrieves the stored refresh token.
     *
     * @return Either a [TokenStorageError] on failure or the refresh token on success
     */
    suspend fun getRefreshToken(): Either<TokenStorageError, String>

    /**
     * Retrieves the expiration timestamp of the stored access token.
     *
     * @return Either a [TokenStorageError] on failure or the expiry timestamp on success
     */
    suspend fun getExpiry(): Either<TokenStorageError, Instant>

    /**
     * Clears all stored tokens from storage.
     * This operation should be performed during logout or when tokens are invalidated.
     *
     * @return Either a [TokenStorageError] on failure or Unit on success
     */
    suspend fun clearTokens(): Either<TokenStorageError, Unit>

}




// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/security/AESCryptoProvider.kt
package eu.torvian.chatbot.server.service.security
import eu.torvian.chatbot.server.domain.security.EncryptionConfig
import java.security.SecureRandom
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
/**
 *   Implementation of [CryptoProvider] using AES encryption.
 *
 *   This class provides a concrete implementation of envelope encryption using AES:
 *   Data is encrypted with a Data Encryption Key (DEK)
 *   The DEK is encrypted with a Key Encryption Key (KEK)
 *
 *   All methods work with Base64-encoded strings to hide implementation details
 *   and decouple the rest of the system from crypto-specific types.
 *
 *   @property config The encryption configuration to use
 */
class AESCryptoProvider(private val config: EncryptionConfig) : CryptoProvider {
    companion object {
        // Use defaults from config if provided, otherwise use these class defaults
        private const val DEFAULT_ALGORITHM = "AES"
        private const val DEFAULT_TRANSFORMATION = "AES/CBC/PKCS5Padding"
        private const val DEFAULT_KEY_SIZE_BITS = 256
        private const val IV_SIZE = 16
    }
    // Properties derived from config or defaults
    private val algorithm: String = config.algorithm ?: DEFAULT_ALGORITHM
    private val transformation: String = config.transformation ?: DEFAULT_TRANSFORMATION
    private val keySizeBits: Int = config.keySizeBits ?: DEFAULT_KEY_SIZE_BITS

    // The KEK is stored internally as a SecretKey, constructed from the Base64-encoded masterKey
    private val kek: SecretKey by lazy {
        val keyBytes = Base64.getDecoder().decode(config.masterKey)
        // Validate key size if necessary, e.g., if keyBytes.size * 8 != keySizeBits
        if (keyBytes.size * 8 != keySizeBits) {
             // Log a warning or throw an exception if the provided key doesn't match the configured size
             // Using a fixed size spec might be safer if keySizeBits is derived from config
            throw IllegalArgumentException("Master key size does not match the configured key size.")
        }
        SecretKeySpec(keyBytes, algorithm)
    }
    /**
     * Generates a new random Data Encryption Key (DEK).
     *
     * @return A Base64-encoded string representation of the DEK.
     */
    override fun generateDEK(): String {
        val keyGenerator = KeyGenerator.getInstance(algorithm)
        keyGenerator.init(keySizeBits, SecureRandom())
        val dek = keyGenerator.generateKey()
        return Base64.getEncoder().encodeToString(dek.encoded)
    }
    /**
     * Encrypts data using the provided DEK.
     *
     * @param plainText The plaintext data to encrypt.
     * @param dek The Base64-encoded DEK to use for encryption.
     * @return A Base64-encoded string containing the encrypted data.
     */
    override fun encryptData(plainText: String, dek: String): String {
        val dekKey = secretKeyFromBase64(dek)
        val cipher = Cipher.getInstance(transformation)
        val iv = ByteArray(IV_SIZE).also { SecureRandom().nextBytes(it) }
        cipher.init(Cipher.ENCRYPT_MODE, dekKey, IvParameterSpec(iv))
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        val combined = iv + encryptedBytes
        return Base64.getEncoder().encodeToString(combined)
    }
    /**
     * Decrypts data using the provided DEK.
     *
     * @param cipherText The Base64-encoded encrypted data.
     * @param dek The Base64-encoded DEK to use for decryption.
     * @return The decrypted plaintext data.
     */
    override fun decryptData(cipherText: String, dek: String): String {
        val dekKey = secretKeyFromBase64(dek)
        val combined = Base64.getDecoder().decode(cipherText)
        // Ensure combined has enough bytes for IV and at least some data
        if (combined.size < IV_SIZE) {
             throw IllegalArgumentException("Ciphertext is too short to contain IV.")
        }
        val iv = combined.copyOfRange(0, IV_SIZE)
        val encryptedBytes = combined.copyOfRange(IV_SIZE, combined.size)
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.DECRYPT_MODE, dekKey, IvParameterSpec(iv))
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }
    /**
     * Encrypts (wraps) a DEK using the KEK.
     *
     * @param dek The Base64-encoded DEK to encrypt.
     * @return A Base64-encoded string containing the encrypted DEK.
     */
    override fun wrapDEK(dek: String): String {
        val dekKey = secretKeyFromBase64(dek)
        val cipher = Cipher.getInstance(transformation)
        val iv = ByteArray(IV_SIZE).also { SecureRandom().nextBytes(it) }
        cipher.init(Cipher.ENCRYPT_MODE, kek, IvParameterSpec(iv))
        val encryptedDekBytes = cipher.doFinal(dekKey.encoded)
        val combined = iv + encryptedDekBytes
        return Base64.getEncoder().encodeToString(combined)
    }
    /**
     * Decrypts (unwraps) a DEK using the KEK.
     *
     * @param wrappedDek The Base64-encoded encrypted DEK.
     * @return The decrypted DEK as a Base64-encoded string.
     */
    override fun unwrapDEK(wrappedDek: String): String {
        val combined = Base64.getDecoder().decode(wrappedDek)
        // Ensure combined has enough bytes for IV and at least some data
         if (combined.size < IV_SIZE) {
             throw IllegalArgumentException("Wrapped DEK is too short to contain IV.")
         }
        val iv = combined.copyOfRange(0, IV_SIZE)
        val encryptedDekBytes = combined.copyOfRange(IV_SIZE, combined.size)
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.DECRYPT_MODE, kek, IvParameterSpec(iv))
        val dekBytes = cipher.doFinal(encryptedDekBytes)
        return Base64.getEncoder().encodeToString(dekBytes)
    }
    /**
     * Gets the current version of the KEK.
     * This can be used to track which version of the KEK was used for encryption.
     *
     * @return The current KEK version.
     */
    override fun getKeyVersion(): Int = config.keyVersion
    /**
     * Converts a Base64-encoded key string to a SecretKey.
     *
     * @param encodedKey The Base64-encoded key string.
     * @return The SecretKey.
     */
    private fun secretKeyFromBase64(encodedKey: String): SecretKey {
        val decoded = Base64.getDecoder().decode(encodedKey)
        return SecretKeySpec(decoded, algorithm)
    }
}
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/security/CryptoProvider.kt
package eu.torvian.chatbot.server.service.security
/**
 * Interface for cryptographic operations.
 *
 * This interface defines the contract for cryptographic providers that implement
 * envelope encryption, where data is encrypted with a Data Encryption Key (DEK)
 * and the DEK is encrypted with a Key Encryption Key (KEK).
 *
 * All methods in this interface work with Base64-encoded strings to hide
 * implementation details and decouple the rest of the system from crypto-specific types.
 */
interface CryptoProvider {
    /**
     * Generates a new random Data Encryption Key (DEK).
     *
     * @return A Base64-encoded string representation of the DEK.
     */
    fun generateDEK(): String
    /**
     * Encrypts data using the provided DEK.
     *
     * @param plainText The plaintext data to encrypt.
     * @param dek The Base64-encoded DEK to use for encryption.
     * @return A Base64-encoded string containing the encrypted data.
     */
    fun encryptData(plainText: String, dek: String): String
    /**
     * Decrypts data using the provided DEK.
     *
     * @param cipherText The Base64-encoded encrypted data.
     * @param dek The Base64-encoded DEK to use for decryption.
     * @return The decrypted plaintext data.
     */
    fun decryptData(cipherText: String, dek: String): String
    /**
     * Encrypts (wraps) a DEK using the KEK.
     *
     * @param dek The Base64-encoded DEK to encrypt.
     * @return A Base64-encoded string containing the encrypted DEK.
     */
    fun wrapDEK(dek: String): String
    /**
     * Decrypts (unwraps) a DEK using the KEK.
     *
     * @param wrappedDek The Base64-encoded encrypted DEK.
     * @return The decrypted DEK as a Base64-encoded string.
     */
    fun unwrapDEK(wrappedDek: String): String
    /**
     * Gets the current version of the KEK.
     * This can be used to track which version of the KEK was used for encryption.
     *
     * @return The current KEK version.
     */
    fun getKeyVersion(): Int
}


// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt
package eu.torvian.chatbot.app.service.auth
import arrow.core.Either
import kotlinx.datetime.Instant
/**
 * Platform-agnostic interface for secure token storage operations.
 *
 * This interface provides a contract for storing and retrieving authentication tokens
 * across different platforms (desktop, web). All operations return Arrow's Either
 * type to provide consistent error handling without exceptions.
 *
 * Implementations should:
 * - Encrypt tokens before storage for security
 * - Handle platform-specific storage mechanisms (files, localStorage, etc.)
 * - Map all storage errors to appropriate [TokenStorageError] types
 * - Provide thread-safe operations where applicable
 */
interface TokenStorage {
    /**
     * Saves authentication tokens to secure storage.
     *
     * @param accessToken The JWT access token for API authentication
     * @param refreshToken The refresh token for obtaining new access tokens
     * @param expiresAt The expiration timestamp for the access token
     * @return Either a [TokenStorageError] on failure or Unit on success
     */
    suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit>
    /**
     * Retrieves the stored access token.
     *
     * @return Either a [TokenStorageError] on failure or the access token on success
     */
    suspend fun getAccessToken(): Either<TokenStorageError, String>
    /**
     * Retrieves the stored refresh token.
     *
     * @return Either a [TokenStorageError] on failure or the refresh token on success
     */
    suspend fun getRefreshToken(): Either<TokenStorageError, String>
    /**
     * Retrieves the expiration timestamp of the stored access token.
     *
     * @return Either a [TokenStorageError] on failure or the expiry timestamp on success
     */
    suspend fun getExpiry(): Either<TokenStorageError, Instant>
    /**
     * Clears all stored tokens from storage.
     * This operation should be performed during logout or when tokens are invalidated.
     *
     * @return Either a [TokenStorageError] on failure or Unit on success
     */
    suspend fun clearTokens(): Either<TokenStorageError, Unit>
    /**
     * Checks if the given token has expired.
     * This method can be used for both access tokens and refresh tokens.
     *
     * For JWT tokens, this method should parse the token's 'exp' claim.
     * For stored tokens, this method should compare against the stored expiry timestamp.
     *
     * @param token The token to check for expiration
     * @return Either a [TokenStorageError] on failure or a Boolean indicating if expired
     */
    suspend fun isTokenExpired(token: String): Either<TokenStorageError, Boolean>
}

// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorageError.kt
package eu.torvian.chatbot.app.service.auth
/**
 * Defines a hierarchy of errors that can occur during token storage operations.
 * These errors follow the same pattern as ApiResourceError and represent
 * logical errors in token management operations.
 */
sealed class TokenStorageError {
    /**
     * A comprehensive, technical message describing the error, suitable for logging and debugging.
     * This message often includes details from the underlying [cause] if available.
     */
    abstract val message: String
    /**
     * The underlying [Throwable] that caused this error, if applicable.
     */
    abstract val cause: Throwable?
    /**
     * Represents an I/O error that occurred during token storage operations.
     * Examples include file system access failures, permission issues, or disk space problems.
     *
     * @property message A descriptive message about the I/O error.
     * @property cause The underlying [Throwable] (e.g., IOException).
     */
    data class IOError(
        override val message: String,
        override val cause: Throwable? = null
    ) : TokenStorageError()
    /**
     * Represents an error that occurred during token encryption or decryption operations.
     * Examples include invalid keys, corrupted encrypted data, or cryptographic failures.
     *
     * @property message A descriptive message about the encryption error.
     * @property cause The underlying [Throwable] (e.g., cryptographic exceptions).
     */
    data class EncryptionError(
        override val message: String,
        override val cause: Throwable? = null
    ) : TokenStorageError()
    /**
     * Represents a case where a requested token was not found in storage.
     * This is a logical error indicating the token has not been stored or has been cleared.
     *
     * @property message A descriptive message about the missing token.
     */
    data class NotFound(
        override val message: String = "Token not found"
    ) : TokenStorageError() {
        override val cause: Throwable? = null
    }
    /**
     * Represents an error parsing or validating token format.
     * Examples include malformed JWT tokens or corrupted token data.
     *
     * @property message A descriptive message about the format error.
     * @property cause The underlying [Throwable] if applicable.
     */
    data class InvalidTokenFormat(
        override val message: String,
        override val cause: Throwable? = null
    ) : TokenStorageError()
    /**
     * Represents any other unexpected or unhandled error in token storage operations.
     *
     * @property message A descriptive message about the unknown error.
     * @property cause The underlying [Throwable] if available.
     */
    data class Unknown(
        override val message: String,
        override val cause: Throwable? = null
    ) : TokenStorageError()
}
// file: server/src/main/kotlin/eu/torvian/chatbot/server/domain/security/EncryptionConfig.kt
package eu.torvian.chatbot.server.domain.security
/**
 * Configuration for encryption-related settings.
 *
 * This class centralizes encryption configuration and provides parameters for
 * encryption operations. It supports the envelope encryption approach
 * where data is encrypted with a Data Encryption Key (DEK) and the DEK is encrypted
 * with a Key Encryption Key (KEK).
 *
 * @property masterKey The Base64-encoded master Key Encryption Key (KEK)
 * @property keyVersion The current version of the KEK
 * @property algorithm The encryption algorithm to use (e.g., "AES"). Defaults to AES in provider.
 * @property transformation The cipher transformation to use (e.g., "AES/CBC/PKCS5Padding"). Defaults to AES/CBC/PKCS5Padding in provider.
 * @property keySizeBits The size of the keys in bits (e.g., 256). Defaults to 256 in provider.
 */
data class EncryptionConfig(
    val masterKey: String, // This needs to be Base64 encoded
    val keyVersion: Int,
    val algorithm: String? = null, // Optional: allow overriding defaults
    val transformation: String? = null, // Optional
    val keySizeBits: Int? = null // Optional
)

```
We want to make some modifications to the class DesktopTokenStorage:
- Use AESCryptoProvider (with EncryptionConfig) to do the encryption/decryption tasks and use envelope encryption (inject CryptoProvider into DesktopTokenStorage)
- Inject String "tokenStoragePath"
- store KEK version with the encrypted DEK
- the DesktopTokenStorage class should only store the DEK, not the KEK



### ASSISTANT
An excellent goal. Refactoring `DesktopTokenStorage` to use an injected `CryptoProvider` and implement envelope encryption is a great way to improve security, modularity, and testability.

Here is the modified code for `DesktopTokenStorage.kt`, updated to meet all your requirements.

### Explanation of Changes

1.  **Dependency Injection**: The class now accepts a `CryptoProvider` and a `tokenStoragePath` string in its constructor, removing the hardcoded file paths and internal encryption logic. This makes the class more flexible and easier to test.
2.  **Envelope Encryption**: The new implementation uses the `CryptoProvider` to perform envelope encryption:
    *   A new Data Encryption Key (DEK) is generated for each `saveTokens` operation.
    *   The token data is encrypted with this DEK.
    *   The DEK itself is encrypted (wrapped) using the master Key Encryption Key (KEK) managed by the `CryptoProvider`.
    *   The KEK is never stored or handled by `DesktopTokenStorage`.
3.  **New Storage Format**: A single JSON file is now used to store a new `EncryptedTokenFile` object. This object contains the encrypted token data, the wrapped DEK, and the version of the KEK that was used to wrap the DEK. This is more robust than storing the key and data in separate files.
4.  **Removed Internal Crypto Logic**: All direct calls to `javax.crypto` have been removed. The old `encrypt`, `decrypt`, and `encryptionKey` members are gone, replaced by calls to the `cryptoProvider`. The crypto-related constants in the `companion object` have also been removed as that responsibility now lies with the `CryptoProvider` implementation.
5.  **Improved Error Handling**: The `try-catch` blocks have been updated to catch more specific exception types (`IOException`, `SerializationException`) instead of relying on parsing exception messages, making the error handling more robust.

***

### Modified Code

This is the new version of `DesktopTokenStorage.kt`. No other files need to be changed.

```kotlin
// file: app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.server.service.security.CryptoProvider
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermissions

/**
 * A serializable container for the encrypted token file content.
 * It stores the encrypted data, the wrapped DEK, and the KEK version used.
 */
@Serializable
private data class EncryptedTokenFile(
    val encryptedData: String, // Base64 encoded encrypted TokenData
    val wrappedDek: String,    // Base64 encoded wrapped Data Encryption Key
    val kekVersion: Int        // Version of the Key Encryption Key used
)

/**
 * Desktop implementation of [TokenStorage] using envelope encryption.
 *
 * Tokens and a wrapped Data Encryption Key (DEK) are stored in a single JSON file
 * at the path provided by [tokenStoragePath]. The file permissions are set to 600
 * (owner read/write only) on POSIX systems.
 *
 * This class relies on an injected [CryptoProvider] to handle all cryptographic
 * operations, including DEK generation, data encryption, and DEK wrapping/unwrapping.
 *
 * @param cryptoProvider The provider for all cryptographic operations.
 * @param tokenStoragePath The absolute path to the file where tokens will be stored.
 * @param json The JSON serializer instance.
 */
open class DesktopTokenStorage(
    private val cryptoProvider: CryptoProvider,
    tokenStoragePath: String,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TokenStorage {

    @Serializable
    private data class TokenData(
        val accessToken: String,
        val refreshToken: String,
        val expiresAt: Long // Instant as epoch seconds
    )

    // The single file for storing the encrypted token data and wrapped key.
    protected open val tokenFile: File = File(tokenStoragePath)

    override suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit> {
        return try {
            // 1. Prepare the plaintext token data for serialization.
            val tokenData = TokenData(
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresAt = expiresAt.epochSeconds
            )
            val plainTextJson = json.encodeToString(tokenData)

            // 2. Perform envelope encryption using the CryptoProvider.
            val dek = cryptoProvider.generateDEK()
            val encryptedData = cryptoProvider.encryptData(plainTextJson, dek)
            val wrappedDek = cryptoProvider.wrapDEK(dek)
            val kekVersion = cryptoProvider.getKeyVersion()

            // 3. Create the container object to be stored on disk.
            val container = EncryptedTokenFile(
                encryptedData = encryptedData,
                wrappedDek = wrappedDek,
                kekVersion = kekVersion
            )
            val fileJson = json.encodeToString(container)

            // 4. Write to disk with secure permissions.
            tokenFile.parentFile?.mkdirs()
            tokenFile.writeText(fileJson)
            setFilePermissions(tokenFile)

            Unit.right()
        } catch (e: Exception) {
            when (e) {
                is IOException, is SecurityException ->
                    TokenStorageError.IOError("Failed to save tokens to file", e).left()
                else -> // Assume other exceptions are from crypto or serialization
                    TokenStorageError.EncryptionError("Failed to encrypt token data for storage", e).left()
            }
        }
    }

    override suspend fun getAccessToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.accessToken }
    }

    override suspend fun getRefreshToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.refreshToken }
    }

    override suspend fun getExpiry(): Either<TokenStorageError, Instant> {
        return getTokenData().map { Instant.fromEpochSeconds(it.expiresAt) }
    }

    override suspend fun clearTokens(): Either<TokenStorageError, Unit> {
        return try {
            if (tokenFile.exists()) {
                tokenFile.delete()
            }
            Unit.right()
        } catch (e: Exception) {
            TokenStorageError.IOError("Failed to clear tokens", e).left()
        }
    }

    private fun getTokenData(): Either<TokenStorageError, TokenData> {
        return try {
            if (!tokenFile.exists()) {
                return TokenStorageError.NotFound("No tokens stored").left()
            }

            // 1. Read the container JSON from the file.
            val fileJson = tokenFile.readText()
            val container = json.decodeFromString<EncryptedTokenFile>(fileJson)

            // Note: A more advanced implementation could use container.kekVersion
            // to select the correct KEK for decryption in a key rotation scenario.

            // 2. Unwrap the DEK and decrypt the token data.
            val dek = cryptoProvider.unwrapDEK(container.wrappedDek)
            val decryptedJson = cryptoProvider.decryptData(container.encryptedData, dek)

            // 3. Deserialize the plaintext JSON back into the TokenData object.
            val tokenData = json.decodeFromString<TokenData>(decryptedJson)

            tokenData.right()
        } catch (e: Exception) {
            when (e) {
                is IOException, is SecurityException ->
                    TokenStorageError.IOError("Failed to read tokens from file", e).left()
                is SerializationException ->
                    TokenStorageError.InvalidTokenFormat("Failed to parse stored token data", e).left()
                else -> // Assume other exceptions are from the decryption process
                    TokenStorageError.EncryptionError("Failed to decrypt token data", e).left()
            }
        }
    }

    private fun setFilePermissions(file: File) {
        try {
            // Set file permissions to 600 (owner read/write only) on Unix-like systems
            if (file.toPath().fileSystem.supportedFileAttributeViews().contains("posix")) {
                Files.setPosixFilePermissions(
                    file.toPath(),
                    PosixFilePermissions.fromString("rw-------")
                )
            }
        } catch (_: Exception) {
            // Ignore permission setting errors on non-POSIX systems (e.g., Windows)
            // The file is still protected by the OS user account permissions
        }
    }
}
```

### USER
when unwrapping we should verify the key version

### ASSISTANT
Of course. That's an excellent and critical security consideration, especially for handling key rotation gracefully. Verifying the KEK version before attempting to decrypt ensures that you fail fast with a clear error message rather than a cryptic cryptographic exception.

Here is the updated `DesktopTokenStorage.kt` with the key version check added to the `getTokenData` function.

### Key Change

In the `getTokenData` function, I've added a check that compares the `kekVersion` from the stored file with the current `kekVersion` from the `cryptoProvider`. If they don't match, it immediately returns an `EncryptionError` with a descriptive message.

```kotlin
// ... inside getTokenData()

// 1. Read the container JSON from the file.
val fileJson = tokenFile.readText()
val container = json.decodeFromString<EncryptedTokenFile>(fileJson)

// 2. Verify that the KEK version used to encrypt matches the current KEK version.
val currentKekVersion = cryptoProvider.getKeyVersion()
if (container.kekVersion != currentKekVersion) {
    return TokenStorageError.EncryptionError(
        "Key version mismatch: stored token was encrypted with key version ${container.kekVersion}, " +
        "but the current key version is $currentKekVersion. Re-authentication is required."
    ).left()
}

// 3. Unwrap the DEK and decrypt the token data.
val dek = cryptoProvider.unwrapDEK(container.wrappedDek)
// ...
```

This prevents the application from trying to decrypt data with the wrong key and provides a clear signal that the stored tokens are stale due to a key rotation, forcing the user to re-authenticate.

### Full Updated File

Here is the complete code for `app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt` with the new verification logic.

```kotlin
// file: app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.server.service.security.CryptoProvider
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermissions

/**
 * A serializable container for the encrypted token file content.
 * It stores the encrypted data, the wrapped DEK, and the KEK version used.
 */
@Serializable
private data class EncryptedTokenFile(
    val encryptedData: String, // Base64 encoded encrypted TokenData
    val wrappedDek: String,    // Base64 encoded wrapped Data Encryption Key
    val kekVersion: Int        // Version of the Key Encryption Key used
)

/**
 * Desktop implementation of [TokenStorage] using envelope encryption.
 *
 * Tokens and a wrapped Data Encryption Key (DEK) are stored in a single JSON file
 * at the path provided by [tokenStoragePath]. The file permissions are set to 600
 * (owner read/write only) on POSIX systems.
 *
 * This class relies on an injected [CryptoProvider] to handle all cryptographic
 * operations, including DEK generation, data encryption, and DEK wrapping/unwrapping.
 *
 * @param cryptoProvider The provider for all cryptographic operations.
 * @param tokenStoragePath The absolute path to the file where tokens will be stored.
 * @param json The JSON serializer instance.
 */
open class DesktopTokenStorage(
    private val cryptoProvider: CryptoProvider,
    tokenStoragePath: String,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TokenStorage {

    @Serializable
    private data class TokenData(
        val accessToken: String,
        val refreshToken: String,
        val expiresAt: Long // Instant as epoch seconds
    )

    // The single file for storing the encrypted token data and wrapped key.
    protected open val tokenFile: File = File(tokenStoragePath)

    override suspend fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresAt: Instant
    ): Either<TokenStorageError, Unit> {
        return try {
            // 1. Prepare the plaintext token data for serialization.
            val tokenData = TokenData(
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresAt = expiresAt.epochSeconds
            )
            val plainTextJson = json.encodeToString(tokenData)

            // 2. Perform envelope encryption using the CryptoProvider.
            val dek = cryptoProvider.generateDEK()
            val encryptedData = cryptoProvider.encryptData(plainTextJson, dek)
            val wrappedDek = cryptoProvider.wrapDEK(dek)
            val kekVersion = cryptoProvider.getKeyVersion()

            // 3. Create the container object to be stored on disk.
            val container = EncryptedTokenFile(
                encryptedData = encryptedData,
                wrappedDek = wrappedDek,
                kekVersion = kekVersion
            )
            val fileJson = json.encodeToString(container)

            // 4. Write to disk with secure permissions.
            tokenFile.parentFile?.mkdirs()
            tokenFile.writeText(fileJson)
            setFilePermissions(tokenFile)

            Unit.right()
        } catch (e: Exception) {
            when (e) {
                is IOException, is SecurityException ->
                    TokenStorageError.IOError("Failed to save tokens to file", e).left()
                else -> // Assume other exceptions are from crypto or serialization
                    TokenStorageError.EncryptionError("Failed to encrypt token data for storage", e).left()
            }
        }
    }

    override suspend fun getAccessToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.accessToken }
    }

    override suspend fun getRefreshToken(): Either<TokenStorageError, String> {
        return getTokenData().map { it.refreshToken }
    }

    override suspend fun getExpiry(): Either<TokenStorageError, Instant> {
        return getTokenData().map { Instant.fromEpochSeconds(it.expiresAt) }
    }

    override suspend fun clearTokens(): Either<TokenStorageError, Unit> {
        return try {
            if (tokenFile.exists()) {
                tokenFile.delete()
            }
            Unit.right()
        } catch (e: Exception) {
            TokenStorageError.IOError("Failed to clear tokens", e).left()
        }
    }

    private fun getTokenData(): Either<TokenStorageError, TokenData> {
        return try {
            if (!tokenFile.exists()) {
                return TokenStorageError.NotFound("No tokens stored").left()
            }

            // 1. Read the container JSON from the file.
            val fileJson = tokenFile.readText()
            val container = json.decodeFromString<EncryptedTokenFile>(fileJson)

            // 2. Verify that the KEK version used to encrypt matches the current KEK version.
            val currentKekVersion = cryptoProvider.getKeyVersion()
            if (container.kekVersion != currentKekVersion) {
                return TokenStorageError.EncryptionError(
                    "Key version mismatch: stored token was encrypted with key version ${container.kekVersion}, " +
                    "but the current key version is $currentKekVersion. Re-authentication is required."
                ).left()
            }

            // 3. Unwrap the DEK and decrypt the token data.
            val dek = cryptoProvider.unwrapDEK(container.wrappedDek)
            val decryptedJson = cryptoProvider.decryptData(container.encryptedData, dek)

            // 4. Deserialize the plaintext JSON back into the TokenData object.
            val tokenData = json.decodeFromString<TokenData>(decryptedJson)

            tokenData.right()
        } catch (e: Exception) {
            when (e) {
                is IOException, is SecurityException ->
                    TokenStorageError.IOError("Failed to read tokens from file", e).left()
                is SerializationException ->
                    TokenStorageError.InvalidTokenFormat("Failed to parse stored token data", e).left()
                else -> // Assume other exceptions are from the decryption process
                    TokenStorageError.EncryptionError("Failed to decrypt token data", e).left()
            }
        }
    }

    private fun setFilePermissions(file: File) {
        try {
            // Set file permissions to 600 (owner read/write only) on Unix-like systems
            if (file.toPath().fileSystem.supportedFileAttributeViews().contains("posix")) {
                Files.setPosixFilePermissions(
                    file.toPath(),
                    PosixFilePermissions.fromString("rw-------")
                )
            }
        } catch (_: Exception) {
            // Ignore permission setting errors on non-POSIX systems (e.g., Windows)
            // The file is still protected by the OS user account permissions
        }
    }
}
```

### USER
what would be the best way to add the option of key rotation?.. I think we need to redesign CryptoProvider, right?.. because when we want to rotate the KEK, we first need to unwrap the DEK with the old KEK… but currently we only have the latest KEK within AESCryptoProvider available

### ASSISTANT
You are absolutely right. Your insight is spot-on and gets to the heart of robust key management. To support key rotation, the `CryptoProvider` must be aware of *previous* keys, not just the current one. The current design with a single `masterKey` in `EncryptionConfig` is insufficient for this.

Let's redesign the `CryptoProvider` and its configuration to properly handle key rotation. This will allow us to decrypt data encrypted with an old KEK while ensuring all *new* data is encrypted with the latest KEK.

Here is the step-by-step plan and the resulting code:

### The Strategy

1.  **Modify `EncryptionConfig`**: It will now hold a map of key versions to Base64-encoded keys, rather than a single key. The `keyVersion` field will now designate which key in the map is the *current* one for encryption.
2.  **Redesign `CryptoProvider` Interface**: The `unwrapDEK` method will be changed to accept the `kekVersion` as an argument. This makes the contract explicit: the caller must provide the version of the key used to wrap the DEK.
3.  **Update `AESCryptoProvider`**: This implementation will now manage a collection of KEKs. It will use the provided `kekVersion` to select the correct key for unwrapping and the configured `current` key for wrapping.
4.  **Update `DesktopTokenStorage`**: The `getTokenData` method will no longer perform a version check itself. Instead, it will pass the `kekVersion` from the stored file to the `cryptoProvider.unwrapDEK` method, delegating the key selection logic entirely.

---

### Step 1: Modify `EncryptionConfig.kt`

We change `masterKey` from a single `String` to a `Map<Int, String>`.

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/domain/security/EncryptionConfig.kt
package eu.torvian.chatbot.server.domain.security

/**
 * Configuration for encryption-related settings.
 *
 * This class centralizes encryption configuration and provides parameters for
 * envelope encryption. It supports key rotation by managing multiple master keys.
 *
 * @property masterKeys A map of KEK versions to their Base64-encoded key values.
 * @property keyVersion The version of the key in [masterKeys] to be used for all new
 *                      encryption operations. This is the "current" or "primary" key.
 * @property algorithm The encryption algorithm to use (e.g., "AES").
 * @property transformation The cipher transformation to use (e.g., "AES/CBC/PKCS5Padding").
 * @property keySizeBits The size of the keys in bits (e.g., 256).
 */
data class EncryptionConfig(
    // CHANGED: From a single masterKey to a map of versioned keys
    val masterKeys: Map<Int, String>,
    val keyVersion: Int, // This now points to the current key in the map
    val algorithm: String? = null,
    val transformation: String? = null,
    val keySizeBits: Int? = null
)
```

---

### Step 2: Redesign `CryptoProvider.kt` Interface

We update the `unwrapDEK` signature.

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/security/CryptoProvider.kt
package eu.torvian.chatbot.server.service.security
// ... (imports and interface KDoc)
interface CryptoProvider {
    // ... (other methods remain the same)

    /**
     * Decrypts (unwraps) a DEK using the KEK of a specific version.
     *
     * @param wrappedDek The Base64-encoded encrypted DEK.
     * @param kekVersion The version of the KEK that was used to wrap this DEK. // ADDED
     * @return The decrypted DEK as a Base64-encoded string.
     */
    // CHANGED: Added kekVersion parameter
    fun unwrapDEK(wrappedDek: String, kekVersion: Int): String

    /**
     * Gets the current version of the KEK used for new encryptions.
     *
     * @return The current KEK version.
     */
    fun getKeyVersion(): Int
}
```

---

### Step 3: Update `AESCryptoProvider.kt` Implementation

This class now handles the logic of selecting the correct key from its collection.

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/security/AESCryptoProvider.kt
package eu.torvian.chatbot.server.service.security

import eu.torvian.chatbot.server.domain.security.EncryptionConfig
import java.security.SecureRandom
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class AESCryptoProvider(private val config: EncryptionConfig) : CryptoProvider {
    companion object {
        private const val DEFAULT_ALGORITHM = "AES"
        private const val DEFAULT_TRANSFORMATION = "AES/CBC/PKCS5Padding"
        private const val DEFAULT_KEY_SIZE_BITS = 256
        private const val IV_SIZE = 16
    }

    private val algorithm: String = config.algorithm ?: DEFAULT_ALGORITHM
    private val transformation: String = config.transformation ?: DEFAULT_TRANSFORMATION
    private val keySizeBits: Int = config.keySizeBits ?: DEFAULT_KEY_SIZE_BITS

    // CHANGED: Store a map of all KEKs, indexed by version
    private val keks: Map<Int, SecretKey> by lazy {
        config.masterKeys.mapValues { (_, keyString) ->
            val keyBytes = Base64.getDecoder().decode(keyString)
            if (keyBytes.size * 8 != keySizeBits) {
                throw IllegalArgumentException("Master key size does not match the configured key size.")
            }
            SecretKeySpec(keyBytes, algorithm)
        }
    }

    // ADDED: A specific reference to the current KEK for new encryptions
    private val currentKek: SecretKey by lazy {
        keks[config.keyVersion]
            ?: throw IllegalStateException("Current key version ${config.keyVersion} not found in master keys map.")
    }

    override fun generateDEK(): String {
        // ... (no change)
        val keyGenerator = KeyGenerator.getInstance(algorithm)
        keyGenerator.init(keySizeBits, SecureRandom())
        val dek = keyGenerator.generateKey()
        return Base64.getEncoder().encodeToString(dek.encoded)
    }

    override fun encryptData(plainText: String, dek: String): String {
        // ... (no change)
        val dekKey = secretKeyFromBase64(dek)
        val cipher = Cipher.getInstance(transformation)
        val iv = ByteArray(IV_SIZE).also { SecureRandom().nextBytes(it) }
        cipher.init(Cipher.ENCRYPT_MODE, dekKey, IvParameterSpec(iv))
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        val combined = iv + encryptedBytes
        return Base64.getEncoder().encodeToString(combined)
    }

    override fun decryptData(cipherText: String, dek: String): String {
        // ... (no change)
        val dekKey = secretKeyFromBase64(dek)
        val combined = Base64.getDecoder().decode(cipherText)
        if (combined.size < IV_SIZE) {
            throw IllegalArgumentException("Ciphertext is too short to contain IV.")
        }
        val iv = combined.copyOfRange(0, IV_SIZE)
        val encryptedBytes = combined.copyOfRange(IV_SIZE, combined.size)
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.DECRYPT_MODE, dekKey, IvParameterSpec(iv))
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }

    override fun wrapDEK(dek: String): String {
        val dekKey = secretKeyFromBase64(dek)
        val cipher = Cipher.getInstance(transformation)
        val iv = ByteArray(IV_SIZE).also { SecureRandom().nextBytes(it) }
        // CHANGED: Always use the 'currentKek' for wrapping
        cipher.init(Cipher.ENCRYPT_MODE, currentKek, IvParameterSpec(iv))
        val encryptedDekBytes = cipher.doFinal(dekKey.encoded)
        val combined = iv + encryptedDekBytes
        return Base64.getEncoder().encodeToString(combined)
    }

    // CHANGED: The signature and implementation of unwrapDEK
    override fun unwrapDEK(wrappedDek: String, kekVersion: Int): String {
        // 1. Select the correct KEK based on the provided version
        val kek = keks[kekVersion]
            ?: throw IllegalArgumentException("KEK for version $kekVersion not found. Cannot decrypt.")

        // 2. Perform decryption with the selected key
        val combined = Base64.getDecoder().decode(wrappedDek)
        if (combined.size < IV_SIZE) {
            throw IllegalArgumentException("Wrapped DEK is too short to contain IV.")
        }
        val iv = combined.copyOfRange(0, IV_SIZE)
        val encryptedDekBytes = combined.copyOfRange(IV_SIZE, combined.size)
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.DECRYPT_MODE, kek, IvParameterSpec(iv))
        val dekBytes = cipher.doFinal(encryptedDekBytes)
        return Base64.getEncoder().encodeToString(dekBytes)
    }

    override fun getKeyVersion(): Int = config.keyVersion

    private fun secretKeyFromBase64(encodedKey: String): SecretKey {
        // ... (no change)
        val decoded = Base64.getDecoder().decode(encodedKey)
        return SecretKeySpec(decoded, algorithm)
    }
}
```

---

### Step 4: Update `DesktopTokenStorage.kt`

This class now correctly delegates the key version logic.

```kotlin
// file: app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt
package eu.torvian.chatbot.app.service.auth

// ... (imports are the same)

// ... (EncryptedTokenFile and TokenData data classes are the same)

open class DesktopTokenStorage(
    private val cryptoProvider: CryptoProvider,
    tokenStoragePath: String,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : TokenStorage {

    // ... (TokenData class and tokenFile property are the same)

    // ... (saveTokens, getAccessToken, getRefreshToken, getExpiry, clearTokens methods are the same)

    private fun getTokenData(): Either<TokenStorageError, TokenData> {
        return try {
            if (!tokenFile.exists()) {
                return TokenStorageError.NotFound("No tokens stored").left()
            }

            // 1. Read the container JSON from the file.
            val fileJson = tokenFile.readText()
            val container = json.decodeFromString<EncryptedTokenFile>(fileJson)

            // 2. Unwrap the DEK by providing the wrapped DEK AND its corresponding KEK version.
            //    The cryptoProvider now handles selecting the correct old key.
            // REMOVED: The manual version check is no longer needed here.
            val dek = cryptoProvider.unwrapDEK(container.wrappedDek, container.kekVersion)
            val decryptedJson = cryptoProvider.decryptData(container.encryptedData, dek)

            // 3. Deserialize the plaintext JSON back into the TokenData object.
            val tokenData = json.decodeFromString<TokenData>(decryptedJson)

            tokenData.right()
        } catch (e: Exception) {
            when (e) {
                is IOException, is SecurityException ->
                    TokenStorageError.IOError("Failed to read tokens from file", e).left()
                is SerializationException ->
                    TokenStorageError.InvalidTokenFormat("Failed to parse stored token data", e).left()
                is IllegalArgumentException -> // Catches the specific error for a missing key version
                    TokenStorageError.EncryptionError(e.message ?: "Failed to find key for decryption", e).left()
                else -> // Assume other exceptions are from the decryption process
                    TokenStorageError.EncryptionError("Failed to decrypt token data", e).left()
            }
        }
    }

    // ... (setFilePermissions method is the same)
}
```

### Next Step: Active Re-encryption (Optional but Recommended)

With this design, you have achieved **passive rotation**: your application can read data encrypted with old keys. However, that data remains encrypted with a potentially retired key.

A best practice is to perform **active re-encryption**. When you successfully read data that was encrypted with an old key, you immediately re-save it. The `saveTokens` method will automatically use the *new* KEK, thus migrating the user's stored tokens to the latest key version.

You can add this logic easily in `getTokenData`:

```kotlin
// In DesktopTokenStorage.getTokenData()

// ... after successfully getting tokenData
val tokenData = json.decodeFromString<TokenData>(decryptedJson)

// ADDED: Active re-encryption logic
if (container.kekVersion != cryptoProvider.getKeyVersion()) {
    // The data was encrypted with an old key. Let's re-encrypt it with the new one.
    // We launch this in the background as it's a write operation during a read.
    // Or, if this class is used in a CoroutineScope, use that scope.
    // For simplicity here, just calling it directly.
    saveTokens(
        accessToken = tokenData.accessToken,
        refreshToken = tokenData.refreshToken,
        expiresAt = Instant.fromEpochSeconds(tokenData.expiresAt)
    )
    // You might want to log this event.
}

tokenData.right()
// ...
```
This self-healing mechanism ensures that over time, all active users' data will be migrated to the newest key without any service disruption.