# Phase 1: Authentication Infrastructure - Development Plan

## Overview

This plan details the implementation of Phase 1 of the user accounts feature, focusing on building the core authentication infrastructure for the Kotlin/Compose chatbot application. This phase establishes the foundation for secure token management, authenticated HTTP communication, and authentication state management without requiring UI changes.

## Task Breakdown

### Task 1: Token Management Infrastructure

#### 1.1 Platform-Agnostic Token Storage Interface

**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt`

**Implementation:**
```kotlin
interface TokenStorage {
    suspend fun saveTokens(accessToken: String, refreshToken: String)
    suspend fun getAccessToken(): String?
    suspend fun getRefreshToken(): String?
    suspend fun clearTokens()
    suspend fun isTokenExpired(token: String): Boolean
}
```

**Deliverables:**
- Interface definition with comprehensive KDoc
- Token expiration validation logic
- Secure token lifecycle management contract

**Acceptance Criteria:**
- [ ] Interface compiles and follows existing codebase patterns
- [ ] All methods are properly documented with KDoc
- [ ] Token expiration logic handles JWT format correctly
- [ ] Interface supports both access and refresh token operations

#### 1.2 Desktop Token Storage Implementation

**Files to Create:**
- `app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt`

**Implementation:**
```kotlin
class DesktopTokenStorage(
    private val logger: KmpLogger = kmpLogger<DesktopTokenStorage>()
) : TokenStorage {
    private val tokenFile = File(System.getProperty("user.home"), ".chatbot/tokens.enc")
    
    override suspend fun saveTokens(accessToken: String, refreshToken: String) {
        // Encrypt and save to file system
    }
    // ... other implementations
}
```

**Platform Considerations:**
- Use file-based storage in user home directory (`.chatbot/tokens.enc`)
- Implement basic encryption for token security
- Handle file permissions and directory creation
- Graceful error handling for file I/O operations

**Acceptance Criteria:**
- [ ] Tokens are encrypted before storage
- [ ] File permissions are set appropriately (600)
- [ ] Directory creation is handled gracefully
- [ ] Error handling for disk space, permissions, etc.
- [ ] Unit tests cover all storage scenarios

#### 1.3 Web Token Storage Implementation

**Files to Create:**
- `app/src/wasmJsMain/kotlin/eu/torvian/chatbot/app/service/auth/WebTokenStorage.kt`

**Implementation:**
```kotlin
class WebTokenStorage(
    private val logger: KmpLogger = kmpLogger<WebTokenStorage>()
) : TokenStorage {
    override suspend fun saveTokens(accessToken: String, refreshToken: String) {
        // Use localStorage with basic encryption
        localStorage.setItem("chatbot_access_token", encrypt(accessToken))
        localStorage.setItem("chatbot_refresh_token", encrypt(refreshToken))
    }
    // ... other implementations
}
```

**Platform Considerations:**
- Use browser localStorage for persistence
- Implement client-side encryption for basic security
- Handle localStorage quota limits
- Consider sessionStorage for sensitive scenarios

**Acceptance Criteria:**
- [ ] Tokens are encrypted before localStorage storage
- [ ] localStorage quota limits are handled
- [ ] Fallback to sessionStorage if localStorage unavailable
- [ ] Unit tests using MockK for localStorage

**Estimated Effort:** 2-3 days
**Dependencies:** None
**Risks:** Platform-specific encryption complexity

---

### Task 2: Authenticated HTTP Client

#### 2.1 Authentication-Aware HTTP Client

**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/AuthenticatedHttpClient.kt`

**Implementation:**
```kotlin
class AuthenticatedHttpClient(
    private val baseClient: HttpClient,
    private val tokenStorage: TokenStorage,
    private val json: Json,
    private val logger: KmpLogger = kmpLogger<AuthenticatedHttpClient>()
) {
    suspend fun <T> authenticatedRequest(
        block: suspend HttpClient.() -> T
    ): Either<ApiResourceError, T> {
        return withContext(ioDispatcher) {
            try {
                val token = tokenStorage.getAccessToken()
                if (token != null && !tokenStorage.isTokenExpired(token)) {
                    executeWithAuth(token, block)
                } else {
                    refreshTokenAndRetry(block)
                }
            } catch (e: Exception) {
                handleAuthenticationError(e)
            }
        }
    }
    
    private suspend fun <T> executeWithAuth(
        token: String, 
        block: suspend HttpClient.() -> T
    ): Either<ApiResourceError, T> {
        // Implementation with Authorization header
    }
}
```

**Key Features:**
- Automatic token injection via Authorization header
- Token refresh logic when access token expires
- Automatic logout on 401 responses
- Integration with existing `safeApiCall` pattern

**Acceptance Criteria:**
- [ ] Authorization header is automatically added to requests
- [ ] Token refresh is triggered before expiration
- [ ] 401 responses trigger automatic logout
- [ ] Integrates seamlessly with existing API clients
- [ ] Comprehensive error handling for auth failures
- [ ] Unit tests cover all authentication scenarios

#### 2.2 HTTP Client Factory Updates

**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt`

**Changes:**
- Add factory method for authenticated HTTP client
- Maintain backward compatibility with existing client
- Support both authenticated and non-authenticated clients

**Acceptance Criteria:**
- [ ] New factory method creates authenticated client
- [ ] Existing factory method remains unchanged
- [ ] Both clients share common configuration
- [ ] No breaking changes to existing API clients

**Estimated Effort:** 3-4 days
**Dependencies:** Task 1 (TokenStorage)
**Risks:** Integration complexity with existing HTTP client configuration

---

### Task 3: Authentication API Client

#### 3.1 Authentication API Client Implementation

**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/AuthApi.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorAuthApiClient.kt`

**Interface Definition:**
```kotlin
interface AuthApi {
    suspend fun login(request: LoginRequest): Either<ApiResourceError, LoginResponse>
    suspend fun register(request: RegisterRequest): Either<ApiResourceError, User>
    suspend fun refreshToken(request: RefreshTokenRequest): Either<ApiResourceError, LoginResponse>
    suspend fun logout(): Either<ApiResourceError, Unit>
    suspend fun logoutAll(): Either<ApiResourceError, Unit>
    suspend fun getCurrentUser(): Either<ApiResourceError, User>
}
```

**Implementation:**
```kotlin
class KtorAuthApiClient(
    client: HttpClient
) : BaseApiResourceClient(client), AuthApi {
    
    override suspend fun login(request: LoginRequest): Either<ApiResourceError, LoginResponse> {
        return safeApiCall {
            client.post(AuthResource.Login()) {
                setBody(request)
            }.body<LoginResponse>()
        }
    }
    // ... other implementations
}
```

**Key Features:**
- Follows existing API client patterns
- Uses type-safe Ktor Resources
- Comprehensive error handling
- Integration with existing `BaseApiResourceClient`

**Acceptance Criteria:**
- [ ] All authentication endpoints are implemented
- [ ] Follows existing API client patterns
- [ ] Uses `safeApiCall` for error handling
- [ ] Proper request/response serialization
- [ ] Unit tests for all endpoints
- [ ] Integration tests with mock server

**Estimated Effort:** 2 days
**Dependencies:** None (uses existing HTTP client)
**Risks:** Low - follows established patterns

---

### Task 4: Authentication Repository

#### 4.1 Authentication Repository Implementation

**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/AuthRepository.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/impl/DefaultAuthRepository.kt`

**Interface Definition:**
```kotlin
interface AuthRepository {
    val authState: StateFlow<AuthState>
    
    suspend fun login(username: String, password: String): Either<RepositoryError, LoginResult>
    suspend fun register(username: String, password: String, email: String?): Either<RepositoryError, User>
    suspend fun logout(): Either<RepositoryError, Unit>
    suspend fun refreshToken(): Either<RepositoryError, LoginResult>
    suspend fun getCurrentUser(): Either<RepositoryError, User>
    suspend fun checkAuthenticationStatus(): Either<RepositoryError, AuthState>
}

sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User, val tokenExpiresAt: Instant) : AuthState()
    data class Error(val error: RepositoryError) : AuthState()
}
```

**Implementation Features:**
- Reactive authentication state using StateFlow
- Token management integration
- Automatic token refresh
- Comprehensive error handling
- Integration with existing repository patterns

**Acceptance Criteria:**
- [ ] Reactive authentication state management
- [ ] Automatic token refresh before expiration
- [ ] Proper error handling and mapping
- [ ] Integration with TokenStorage
- [ ] Unit tests with MockK
- [ ] Follows existing repository patterns

**Estimated Effort:** 3 days
**Dependencies:** Task 1 (TokenStorage), Task 3 (AuthApi)
**Risks:** State management complexity

---

## Implementation Order & Dependencies

```mermaid
graph TD
    A[Task 1.1: TokenStorage Interface] --> B[Task 1.2: Desktop TokenStorage]
    A --> C[Task 1.3: Web TokenStorage]
    B --> D[Task 2.1: AuthenticatedHttpClient]
    C --> D
    D --> E[Task 2.2: HTTP Client Factory Updates]
    F[Task 3.1: AuthApi Client] --> G[Task 4.1: AuthRepository]
    A --> G
    E --> H[Integration & Testing]
    G --> H
```

**Recommended Implementation Sequence:**
1. **Week 1**: Task 1 (Token Management) - All subtasks in parallel
2. **Week 2**: Task 2 (Authenticated HTTP Client) - Sequential implementation
3. **Week 3**: Task 3 (Auth API Client) + Task 4 (Auth Repository) - Can be done in parallel
4. **Week 4**: Integration, testing, and refinement

---

## Testing Strategy

### Unit Tests

**Token Storage Tests:**
```kotlin
// app/src/commonTest/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorageTest.kt
class TokenStorageTest {
    @Test
    fun `saveTokens should store tokens securely`()
    @Test
    fun `getAccessToken should return stored token`()
    @Test
    fun `isTokenExpired should validate JWT expiration`()
    @Test
    fun `clearTokens should remove all stored tokens`()
}
```

**Authenticated HTTP Client Tests:**
```kotlin
// app/src/commonTest/kotlin/eu/torvian/chatbot/app/service/auth/AuthenticatedHttpClientTest.kt
class AuthenticatedHttpClientTest {
    @Test
    fun `authenticatedRequest should add Authorization header`()
    @Test
    fun `authenticatedRequest should refresh expired tokens`()
    @Test
    fun `authenticatedRequest should handle 401 responses`()
}
```

**Repository Tests:**
```kotlin
// app/src/commonTest/kotlin/eu/torvian/chatbot/app/repository/impl/DefaultAuthRepositoryTest.kt
class DefaultAuthRepositoryTest {
    @Test
    fun `login should update auth state on success`()
    @Test
    fun `logout should clear tokens and update state`()
    @Test
    fun `authState should emit correct states`()
}
```

### Integration Tests

**Authentication Flow Tests:**
- End-to-end login flow
- Token refresh scenarios
- Logout and session cleanup
- Error handling and recovery

**Platform-Specific Tests:**
- Desktop: File system token storage
- Web: localStorage token storage
- Cross-platform: Token encryption/decryption

---

## Platform Considerations

### Desktop Platform
- **Token Storage**: Encrypted file in `~/.chatbot/tokens.enc`
- **Security**: File permissions (600), directory creation
- **Dependencies**: File I/O, basic encryption utilities

### Web Platform (WASM)
- **Token Storage**: localStorage with client-side encryption
- **Security**: XSS protection, secure token handling
- **Dependencies**: Browser APIs, localStorage availability

### Common Considerations
- **JWT Handling**: Token parsing and expiration validation
- **Error Recovery**: Network failures, token corruption
- **Performance**: Minimal overhead for token operations

---

## Error Handling Approaches

### Network Failures
```kotlin
sealed class AuthError : RepositoryError {
    data class NetworkError(override val message: String, override val cause: Throwable?) : AuthError()
    data class InvalidCredentials(override val message: String) : AuthError()
    data class TokenExpired(override val message: String) : AuthError()
    data class ServerError(val apiError: ApiError) : AuthError()
}
```

### Token Management Errors
- **Storage Failures**: Disk space, permissions, corruption
- **Encryption Errors**: Key derivation, cipher failures
- **Expiration Handling**: Grace periods, refresh timing

### Recovery Strategies
- **Automatic Retry**: Network timeouts, temporary failures
- **Graceful Degradation**: Offline mode, cached data
- **User Notification**: Clear error messages, actionable steps

---

## Integration Points

### Koin Dependency Injection Updates

**Module Changes:**
```kotlin
// app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
fun appModule(baseUri: String): Module = module {
    // Token Storage (platform-specific)
    single<TokenStorage> { 
        if (Platform.isDesktop) DesktopTokenStorage()
        else WebTokenStorage()
    }
    
    // Authenticated HTTP Client
    single<AuthenticatedHttpClient> {
        AuthenticatedHttpClient(get(), get(), get())
    }
    
    // Authentication API Client
    single<AuthApi> {
        KtorAuthApiClient(get<AuthenticatedHttpClient>().baseClient)
    }
    
    // Authentication Repository
    single<AuthRepository> {
        DefaultAuthRepository(get(), get())
    }
}
```

### Existing Repository Integration
- **Error Handling**: Extend existing `RepositoryError` hierarchy
- **State Management**: Follow existing `StateFlow<DataState<T>>` patterns
- **API Integration**: Use existing `BaseApiResourceClient` patterns

---

## Acceptance Criteria Summary

### Task 1: Token Management
- [ ] Platform-agnostic TokenStorage interface
- [ ] Desktop implementation with file-based encrypted storage
- [ ] Web implementation with localStorage encryption
- [ ] JWT expiration validation
- [ ] Comprehensive unit tests for all platforms

### Task 2: Authenticated HTTP Client
- [ ] Automatic Authorization header injection
- [ ] Token refresh before expiration
- [ ] 401 response handling with automatic logout
- [ ] Integration with existing API client patterns
- [ ] Comprehensive error handling

### Task 3: Authentication API Client
- [ ] All auth endpoints implemented (login, register, refresh, logout)
- [ ] Follows existing API client patterns
- [ ] Type-safe request/response handling
- [ ] Integration with BaseApiResourceClient

### Task 4: Authentication Repository
- [ ] Reactive authentication state management
- [ ] Token lifecycle management
- [ ] Error handling and mapping
- [ ] Integration with existing repository patterns

### Overall Integration
- [ ] Koin DI modules updated
- [ ] No breaking changes to existing code
- [ ] Comprehensive test coverage (>90%)
- [ ] Platform-specific implementations working
- [ ] Error handling consistent with existing patterns

---

## Estimated Effort & Risks

### Total Estimated Effort: 12-15 days

**Task Breakdown:**
- Task 1 (Token Management): 3-4 days
- Task 2 (Authenticated HTTP Client): 3-4 days  
- Task 3 (Authentication API Client): 2 days
- Task 4 (Authentication Repository): 3 days
- Integration & Testing: 2-3 days

### Potential Risks & Mitigation

**High Risk:**
- **Platform-specific encryption complexity**
  - *Mitigation*: Use established encryption libraries, start with basic implementation
  
**Medium Risk:**
- **Token refresh timing and race conditions**
  - *Mitigation*: Implement proper synchronization, comprehensive testing
  
- **Integration with existing HTTP client configuration**
  - *Mitigation*: Maintain backward compatibility, incremental changes

**Low Risk:**
- **API client implementation** (follows established patterns)
- **Repository pattern implementation** (well-established in codebase)

### Success Metrics
- [ ] All unit tests passing (>95% coverage)
- [ ] Integration tests passing on both platforms
- [ ] No breaking changes to existing functionality
- [ ] Performance impact < 5% for authenticated requests
- [ ] Memory usage increase < 10MB for token storage

This plan provides a solid foundation for implementing authentication infrastructure while maintaining compatibility with the existing codebase architecture and patterns.
