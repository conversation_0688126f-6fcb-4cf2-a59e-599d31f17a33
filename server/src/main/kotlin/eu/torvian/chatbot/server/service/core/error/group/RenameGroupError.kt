package eu.torvian.chatbot.server.service.core.error.group

/**
 * Represents possible errors when renaming a chat group.
 */
sealed interface RenameGroupError {
    /**
     * Indicates that the group with the specified ID was not found.
     * Maps from GroupError.GroupNotFound in the DAO layer.
     */
    data class GroupNotFound(val id: Long) : RenameGroupError
    /**
     * Indicates that the provided new name is invalid (e.g., blank).
     */
    data class InvalidName(val reason: String) : RenameGroupError

    /**
     * Indicates that the user does not have access to rename the requested group.
     */
    data class AccessDenied(val reason: String) : RenameGroupError
}
